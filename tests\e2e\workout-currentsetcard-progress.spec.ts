import { test, expect } from '@playwright/test'

test.describe('CurrentSetCard Progress Bar', () => {
  const TEST_USER = {
    email: '<EMAIL>',
    password: 'Dr123456',
  }

  test.beforeEach(async ({ page }) => {
    // Login before each test
    await page.goto('/login')
    await page.getByLabel('Email').fill(TEST_USER.email)
    await page.getByLabel('Password').fill(TEST_USER.password)
    await page.getByRole('button', { name: /sign in/i }).click()
    await page.waitForURL('/program', { timeout: 30000 })
  })

  test('should display only one progress bar at the top of CurrentSetCard', async ({
    page,
  }) => {
    // Given: User starts a workout and navigates to exercise page
    await page
      .getByRole('button', { name: /start workout|today's workout/i })
      .click()
    await page.waitForURL('/workout')

    // Click on first exercise to navigate to exercise-v2 page
    await page.locator('[data-testid="exercise-item"]').first().click()

    // Wait for exercise page to load (could be either exercise or exercise-v2)
    await page.waitForURL(/\/workout\/exercise(-v2)?\/\d+/, { timeout: 10000 })

    // When: Viewing the CurrentSetCard component (if it exists on this page)
    const currentSetCard = page.locator('[data-testid="current-set-card"]')
    const hasCurrentSetCard = (await currentSetCard.count()) > 0

    if (hasCurrentSetCard) {
      // Check for progress bars
      const progressBars = await page.$$(
        '.h-2.bg-surface-secondary.rounded-full'
      )

      // Then: Should have only ONE progress bar
      expect(progressBars.length).toBe(1)

      // And: Progress bar should be visible at the top
      const progressBar = progressBars[0]
      const isVisible = await progressBar.isVisible()
      expect(isVisible).toBe(true)

      // And: Progress bar should be above the swipe text if it exists
      const swipeText = page.locator(
        'text=Swipe left to skip · right to complete'
      )
      const hasSwipeText = (await swipeText.count()) > 0

      if (hasSwipeText) {
        const swipeTextBBox = await swipeText.boundingBox()
        const progressBarBBox = await progressBar.boundingBox()
        expect(progressBarBBox?.y).toBeLessThan(swipeTextBBox?.y || 0)
      }
    } else {
      // Skip test if CurrentSetCard is not present
      test.skip()
    }
  })
})
