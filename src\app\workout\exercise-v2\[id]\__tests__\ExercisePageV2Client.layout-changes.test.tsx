import { describe, it, expect, vi } from 'vitest'
import { render, screen } from '@testing-library/react'
import { ExercisePageV2Client } from '../ExercisePageV2Client'

// Mock all dependencies
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: vi.fn(),
  }),
}))

vi.mock('@/contexts/NavigationContext', () => ({
  useNavigation: () => ({
    setTitle: vi.fn(),
    setSetInfo: vi.fn(),
    setSetProgress: vi.fn(),
  }),
}))

vi.mock('@/stores/authStore', () => ({
  useAuthStore: () => ({
    getCachedUserInfo: () => ({ MassUnit: 'kg' }),
  }),
}))

vi.mock('@/hooks/useWorkout', () => ({
  useWorkout: () => ({
    isLoadingWorkout: false,
    workoutError: null,
    workoutSession: { id: '123' },
    saveSet: vi.fn(),
  }),
}))

vi.mock('@/stores/workoutStore', () => ({
  useWorkoutStore: () => ({
    loadingStates: new Map(),
    nextSet: vi.fn(),
  }),
}))

vi.mock('@/hooks/useExercisePageInitialization', () => ({
  useExercisePageInitialization: () => ({
    isInitializing: false,
    loadingError: null,
    retryInitialization: vi.fn(),
  }),
}))

vi.mock('@/hooks/useSetScreenLogic', () => ({
  useSetScreenLogic: () => ({
    currentExercise: { Id: 1, Label: 'Bench Press' },
    exercises: [{ Id: 1, Label: 'Bench Press' }],
    currentSetIndex: 0,
    isSaving: false,
    saveError: null,
    showComplete: false,
    showExerciseComplete: false,
    recommendation: { WeightIncrement: 5 },
    isLoading: false,
    error: null,
    isLastExercise: false,
    isLastSet: false,
    isWarmup: false,
    isFirstWorkSet: false,
    completedSets: [],
    setData: { reps: 10, weight: 80, duration: 0 },
    setSetData: vi.fn(),
    setSaveError: vi.fn(),
    handleSaveSet: vi.fn(),
    refetchRecommendation: vi.fn(),
  }),
}))

vi.mock('@/components/workout-v2/RestTimer', () => ({
  RestTimer: () => <div data-testid="rest-timer">Rest Timer</div>,
  useRestTimer: () => ({ startRestTimer: vi.fn() }),
}))

vi.mock('@/components/workout-v2/CurrentSetCard', () => ({
  CurrentSetCard: () => (
    <div data-testid="current-set-card">Current Set Card</div>
  ),
}))

vi.mock('@/components/workout-v2/ExerciseQuickNav', () => ({
  ExerciseQuickNav: () => (
    <div data-testid="exercise-quick-nav">Exercise Quick Nav</div>
  ),
}))

vi.mock('@/utils/generateAllSets', () => ({
  generateAllSets: () => [
    { Id: 1, IsNext: true, IsWarmups: false, IsFinished: false },
  ],
}))

describe('ExercisePageV2Client - Layout Changes', () => {
  it('should structure layout for standard navigation integration', () => {
    // Given: Exercise page renders
    render(<ExercisePageV2Client exerciseId={1} />)

    // When: Page loads
    // Then: Should have proper content structure that works with standard nav
    // The outer container should have flex layout classes
    const outerContainer = screen
      .getByTestId('exercise-quick-nav')
      .closest('.h-screen')
    expect(outerContainer).toHaveClass(
      'flex',
      'flex-col',
      'h-screen',
      'bg-surface-primary'
    )
  })

  it('should position exercise quick nav under standard navigation area', () => {
    // Given: Exercise page with navigation
    render(<ExercisePageV2Client exerciseId={1} />)

    // When: Quick nav renders
    const quickNav = screen.getByTestId('exercise-quick-nav')

    // Then: Should be positioned to account for standard nav bar
    expect(quickNav).toBeInTheDocument()
    // Quick nav should be first element after navigation space
    expect(quickNav).toBeTruthy()
  })

  it('should position rest timer at bottom under current set', () => {
    // Given: Exercise page with rest timer
    render(<ExercisePageV2Client exerciseId={1} />)

    // When: Rest timer and current set render
    const restTimer = screen.getByTestId('rest-timer')
    const currentSetCard = screen.getByTestId('current-set-card')

    // Then: Rest timer should be positioned after current set in DOM order
    expect(restTimer).toBeInTheDocument()
    expect(currentSetCard).toBeInTheDocument()

    // Timer should come after set card in layout flow
    const mainContent = restTimer.closest('.flex-1')
    expect(mainContent).toContainElement(restTimer)
    expect(mainContent).toContainElement(currentSetCard)
  })

  it('should maintain proper spacing with standard navigation', () => {
    // Given: Exercise page structure
    render(<ExercisePageV2Client exerciseId={1} />)

    // When: Content renders
    const mainContainer = screen
      .getByTestId('current-set-card')
      .closest('.flex-1')

    // Then: Should have proper spacing classes for navigation
    expect(mainContainer).toHaveClass('flex-1')
    expect(mainContainer).toHaveClass('flex', 'flex-col')
  })

  it('should not use custom navigation that conflicts with standard nav', () => {
    // Given: Exercise page renders
    render(<ExercisePageV2Client exerciseId={1} />)

    // When: Page structure is examined
    // Then: Should not create additional nav elements that conflict
    const outerContainer = screen
      .getByTestId('exercise-quick-nav')
      .closest('.h-screen')

    // Should use bg-surface-primary instead of custom nav styling
    expect(outerContainer).toHaveClass('bg-surface-primary')
  })

  it('should position ExerciseQuickNav at bottom of page below CurrentSetCard', () => {
    // Given: Exercise page renders with all components
    render(<ExercisePageV2Client exerciseId={1} />)

    // When: Examining component order
    const quickNav = screen.getByTestId('exercise-quick-nav')
    const currentSetCard = screen.getByTestId('current-set-card')

    // Then: ExerciseQuickNav should come AFTER CurrentSetCard in DOM order
    expect(currentSetCard.compareDocumentPosition(quickNav)).toBe(
      Node.DOCUMENT_POSITION_FOLLOWING
    )
  })

  it('should not have overflow-y-auto on NextSetsPreview container to prevent WheelPicker conflicts', () => {
    // Given: Exercise page is rendered with all components
    render(<ExercisePageV2Client exerciseId={1} />)

    // When: Examining the main content area for scroll containers
    const container = screen.getByTestId('exercise-page-container')

    // Then: No overflow-y-auto should be present on any child containers
    // This test will fail initially until overflow-y-auto is removed from line 241
    const scrollContainers = container.querySelectorAll(
      '[class*="overflow-y-auto"]'
    )
    expect(scrollContainers).toHaveLength(0)
  })
})
