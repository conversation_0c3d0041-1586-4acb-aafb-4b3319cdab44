import { describe, it, expect, vi } from 'vitest'
import { render, screen } from '@testing-library/react'
import { CurrentSetCard } from '../CurrentSetCard'
import type {
  ExerciseModel,
  WorkoutLogSerieModel,
  RecommendationModel,
} from '@/types'

// Mock WheelPicker component
vi.mock('@/components/ui/WheelPicker', () => ({
  WheelPicker: ({ value, label }: { value: number; label: string }) => (
    <div data-testid={`wheel-picker-${label.toLowerCase()}`}>{value}</div>
  ),
}))

// Mock haptics
vi.mock('@/utils/haptics', () => ({
  vibrate: vi.fn(),
}))

// Mock useSetMetrics hook - this will be mocked for different test scenarios
vi.mock('@/hooks/useSetMetrics')

describe('CurrentSetCard Metrics Display', () => {
  const mockExercise: ExerciseModel = {
    Id: 1,
    Label: 'Test Exercise',
    BodyPart: 'Chest',
    Equipment: 'Barbell',
  } as ExerciseModel

  const mockCurrentSet: WorkoutLogSerieModel = {
    Id: 1,
    Reps: 10,
    Weight: { Kg: 100, Lb: 220 },
    IsWarmups: false,
  } as WorkoutLogSerieModel

  const mockRecommendation: RecommendationModel = {
    Id: 1,
    WarmupsCount: 2,
    Series: 3,
  } as RecommendationModel

  const defaultProps = {
    exercise: mockExercise,
    currentSet: mockCurrentSet,
    setData: { reps: 10, weight: 100, duration: 0 },
    onSetDataChange: vi.fn(),
    onComplete: vi.fn(),
    isSaving: false,
    completedSets: 2,
    unit: 'kg' as const,
    recommendation: mockRecommendation,
    currentSetIndex: 2,
    isWarmup: false,
    isFirstWorkSet: true,
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('Last time display', () => {
    it('should display "Last time" when metrics are available', async () => {
      const { useSetMetrics } = await import('@/hooks/useSetMetrics')
      vi.mocked(useSetMetrics).mockReturnValue({
        lastTimeReps: 12,
        lastTimeWeight: 60,
        oneRMProgress: null,
      })

      render(<CurrentSetCard {...defaultProps} />)

      expect(screen.getByText(/Last time:/)).toBeInTheDocument()
      expect(screen.getByText(/12 × 60 kg/)).toBeInTheDocument()
    })

    it('should display correct unit for last time weight', async () => {
      const { useSetMetrics } = await import('@/hooks/useSetMetrics')
      vi.mocked(useSetMetrics).mockReturnValue({
        lastTimeReps: 8,
        lastTimeWeight: 132,
        oneRMProgress: null,
      })

      render(<CurrentSetCard {...defaultProps} unit="lbs" />)

      expect(screen.getByText(/8 × 132 lbs/)).toBeInTheDocument()
    })

    it('should not display "Last time" when no data available', async () => {
      const { useSetMetrics } = await import('@/hooks/useSetMetrics')
      vi.mocked(useSetMetrics).mockReturnValue({
        lastTimeReps: null,
        lastTimeWeight: null,
        oneRMProgress: null,
      })

      render(<CurrentSetCard {...defaultProps} />)

      expect(screen.queryByText(/Last time:/)).not.toBeInTheDocument()
    })
  })

  describe('1RM Progress display', () => {
    it('should display positive 1RM progress', async () => {
      const { useSetMetrics } = await import('@/hooks/useSetMetrics')
      vi.mocked(useSetMetrics).mockReturnValue({
        lastTimeReps: 12,
        lastTimeWeight: 60,
        oneRMProgress: 15.75,
      })

      render(<CurrentSetCard {...defaultProps} />)

      expect(screen.getByText(/1RM Progress:/)).toBeInTheDocument()
      expect(screen.getByText(/\+15\.75%/)).toBeInTheDocument()
    })

    it('should display negative 1RM progress without double negative', async () => {
      const { useSetMetrics } = await import('@/hooks/useSetMetrics')
      vi.mocked(useSetMetrics).mockReturnValue({
        lastTimeReps: 12,
        lastTimeWeight: 60,
        oneRMProgress: -5.25,
      })

      render(<CurrentSetCard {...defaultProps} />)

      expect(screen.getByText(/1RM Progress:/)).toBeInTheDocument()
      expect(screen.getByText(/-5\.25%/)).toBeInTheDocument()
      expect(screen.queryByText(/\+-5\.25%/)).not.toBeInTheDocument()
    })

    it('should not display 1RM progress when not available', async () => {
      const { useSetMetrics } = await import('@/hooks/useSetMetrics')
      vi.mocked(useSetMetrics).mockReturnValue({
        lastTimeReps: 12,
        lastTimeWeight: 60,
        oneRMProgress: null,
      })

      render(<CurrentSetCard {...defaultProps} />)

      expect(screen.queryByText(/1RM Progress:/)).not.toBeInTheDocument()
    })

    it('should only show 1RM progress for first work set', async () => {
      const { useSetMetrics } = await import('@/hooks/useSetMetrics')
      vi.mocked(useSetMetrics).mockReturnValue({
        lastTimeReps: 10,
        lastTimeWeight: 65,
        oneRMProgress: null, // Should be null for non-first work sets
      })

      render(<CurrentSetCard {...defaultProps} isFirstWorkSet={false} />)

      expect(screen.getByText(/Last time:/)).toBeInTheDocument()
      expect(screen.queryByText(/1RM Progress:/)).not.toBeInTheDocument()
    })
  })

  describe('metrics integration', () => {
    it('should pass correct parameters to useSetMetrics hook', async () => {
      const { useSetMetrics } = await import('@/hooks/useSetMetrics')
      const mockUseSetMetrics = vi.mocked(useSetMetrics)
      mockUseSetMetrics.mockReturnValue({
        lastTimeReps: null,
        lastTimeWeight: null,
        oneRMProgress: null,
      })

      render(<CurrentSetCard {...defaultProps} />)

      expect(mockUseSetMetrics).toHaveBeenCalledWith({
        recommendation: mockRecommendation,
        currentSetIndex: 2,
        isWarmup: false,
        unit: 'kg',
        isFirstWorkSet: true,
        currentReps: 10,
        currentWeight: 100,
      })
    })

    it('should handle warmup set correctly', async () => {
      const { useSetMetrics } = await import('@/hooks/useSetMetrics')
      const mockUseSetMetrics = vi.mocked(useSetMetrics)
      mockUseSetMetrics.mockReturnValue({
        lastTimeReps: 8,
        lastTimeWeight: 40,
        oneRMProgress: null,
      })

      const warmupSet = { ...mockCurrentSet, IsWarmups: true }

      render(
        <CurrentSetCard
          {...defaultProps}
          currentSet={warmupSet}
          isWarmup
          isFirstWorkSet={false}
          currentSetIndex={0}
        />
      )

      expect(mockUseSetMetrics).toHaveBeenCalledWith({
        recommendation: mockRecommendation,
        currentSetIndex: 0,
        isWarmup: true,
        unit: 'kg',
        isFirstWorkSet: false,
        currentReps: 10,
        currentWeight: 100,
      })
    })

    it('should handle missing recommendation gracefully', async () => {
      const { useSetMetrics } = await import('@/hooks/useSetMetrics')
      const mockUseSetMetrics = vi.mocked(useSetMetrics)
      mockUseSetMetrics.mockReturnValue({
        lastTimeReps: null,
        lastTimeWeight: null,
        oneRMProgress: null,
      })

      render(<CurrentSetCard {...defaultProps} recommendation={null} />)

      expect(mockUseSetMetrics).toHaveBeenCalledWith({
        recommendation: null,
        currentSetIndex: 2,
        isWarmup: false,
        unit: 'kg',
        isFirstWorkSet: true,
        currentReps: 10,
        currentWeight: 100,
      })

      expect(screen.queryByText(/Last time:/)).not.toBeInTheDocument()
      expect(screen.queryByText(/1RM Progress:/)).not.toBeInTheDocument()
    })
  })

  describe('styling and layout', () => {
    it('should apply correct styling to metrics section', async () => {
      const { useSetMetrics } = await import('@/hooks/useSetMetrics')
      vi.mocked(useSetMetrics).mockReturnValue({
        lastTimeReps: 12,
        lastTimeWeight: 60,
        oneRMProgress: 10.5,
      })

      render(<CurrentSetCard {...defaultProps} />)

      const metricsSection = screen.getByTestId('set-metrics')
      expect(metricsSection).toHaveClass('text-center', 'space-y-1', 'mb-4')
    })

    it('should style 1RM progress as brand primary', async () => {
      const { useSetMetrics } = await import('@/hooks/useSetMetrics')
      vi.mocked(useSetMetrics).mockReturnValue({
        lastTimeReps: 12,
        lastTimeWeight: 60,
        oneRMProgress: 10.5,
      })

      render(<CurrentSetCard {...defaultProps} />)

      const progressElement = screen.getByText(/1RM Progress:/)
      expect(progressElement).toHaveClass('text-brand-primary', 'font-medium')
    })

    it('should style last time as secondary text', async () => {
      const { useSetMetrics } = await import('@/hooks/useSetMetrics')
      vi.mocked(useSetMetrics).mockReturnValue({
        lastTimeReps: 12,
        lastTimeWeight: 60,
        oneRMProgress: null,
      })

      render(<CurrentSetCard {...defaultProps} />)

      const lastTimeElement = screen.getByText(/Last time:/)
      expect(lastTimeElement).toHaveClass('text-text-secondary', 'text-sm')
    })
  })
})
