'use client'

import React, { useRef, useEffect, useState } from 'react'
import { usePathname } from 'next/navigation'
import { ChevronLeftIcon } from '@/components/icons'

interface IOSNavigationBarProps {
  title: string
  showBackButton?: boolean
  onBackClick?: () => void
  leftElement?: React.ReactNode
  rightElement?: React.ReactNode
  className?: string
  isExercisePage?: boolean
  setInfo?: string
  progressValue?: number
  totalSets?: number
  completedSets?: number
  titleDisplayMode?:
    | 'auto-scroll'
    | 'multiline'
    | 'swipe-scroll'
    | 'smart-truncate'
}

export function IOSNavigationBar({
  title,
  showBackButton = false,
  onBackClick,
  leftElement,
  rightElement,
  className = '',
  isExercisePage = false,
  setInfo,
  progressValue,
  totalSets,
  completedSets,
  titleDisplayMode = 'auto-scroll',
}: IOSNavigationBarProps) {
  const titleRef = useRef<HTMLHeadingElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)
  const [needsScroll, setNeedsScroll] = useState(false)
  const [isPaused, setIsPaused] = useState(false)
  const pathname = usePathname()

  // Hide setInfo on old exercise pages (not v2)
  const isOldExercisePage =
    pathname.includes('/workout/exercise/') &&
    !pathname.includes('/workout/exercise-v2/')
  const shouldShowSetInfo = setInfo && !isOldExercisePage

  useEffect(() => {
    if (
      titleRef.current &&
      containerRef.current &&
      isExercisePage &&
      titleDisplayMode === 'auto-scroll'
    ) {
      const titleWidth = titleRef.current.scrollWidth
      const containerWidth = containerRef.current.clientWidth
      setNeedsScroll(titleWidth > containerWidth)
    }
  }, [title, isExercisePage, titleDisplayMode])

  const renderExerciseTitle = () => {
    if (titleDisplayMode === 'multiline') {
      return (
        <h1
          ref={titleRef}
          className="text-4xl font-bold text-text-primary line-clamp-2 whitespace-normal leading-tight"
        >
          {title}
        </h1>
      )
    }

    if (titleDisplayMode === 'swipe-scroll') {
      return (
        <div
          className="overflow-x-auto scrollbar-hide"
          data-swipe-enabled="true"
        >
          <h1
            ref={titleRef}
            className="text-4xl font-bold text-text-primary whitespace-nowrap"
          >
            {title}
          </h1>
        </div>
      )
    }

    if (titleDisplayMode === 'smart-truncate') {
      return (
        <h1
          ref={titleRef}
          className="text-4xl font-bold text-text-primary truncate"
          data-tooltip-content={title}
          data-expandable="true"
        >
          {title}
        </h1>
      )
    }

    // Default: enhanced auto-scroll
    return (
      <div className="relative overflow-hidden">
        <h1
          ref={titleRef}
          className={`text-4xl font-bold text-text-primary ${
            needsScroll
              ? `animate-scroll-enhanced whitespace-nowrap ${
                  isPaused ? 'animation-play-state-paused' : ''
                }`
              : 'truncate'
          }`}
          style={
            needsScroll
              ? ({
                  '--scroll-width': `${titleRef.current?.scrollWidth || 0}px`,
                } as React.CSSProperties)
              : {}
          }
          data-scroll-pausable="true"
          onTouchStart={() => setIsPaused(true)}
          onTouchEnd={() => setIsPaused(false)}
          onMouseEnter={() => setIsPaused(true)}
          onMouseLeave={() => setIsPaused(false)}
        >
          {title}
        </h1>
      </div>
    )
  }
  return (
    <>
      <header
        className={`fixed top-0 left-0 right-0 z-50 backdrop-blur-md bg-bg-primary/80 ${className}`}
      >
        <div className="flex items-center justify-between h-[66px] px-4">
          {/* Left Section */}
          <div
            className={`${isExercisePage ? 'flex-shrink-0' : 'flex-1'} flex items-center`}
          >
            {leftElement ||
              (showBackButton && (
                <button
                  onClick={() => {
                    // Add haptic feedback
                    if ('vibrate' in navigator) {
                      navigator.vibrate(10)
                    }
                    onBackClick?.()
                  }}
                  className="flex items-center -ml-2 px-2 py-2 rounded-lg transition-colors hover:bg-bg-secondary"
                  aria-label="Go back"
                >
                  <ChevronLeftIcon
                    size={isExercisePage ? 36 : 28}
                    className="text-brand-primary"
                  />
                </button>
              ))}
          </div>

          {/* Center Section - Title */}
          <div
            className={`${
              isExercisePage ? 'flex-1 min-w-0' : 'flex-1'
            } flex items-center ${
              isExercisePage ? 'justify-start' : 'justify-center'
            }`}
            ref={containerRef}
          >
            {isExercisePage ? (
              <div className="flex flex-col min-w-0">
                {renderExerciseTitle()}
                {!isOldExercisePage && (
                  <div className="flex items-center gap-2 mt-1">
                    {shouldShowSetInfo && (
                      <span className="text-sm text-text-secondary">
                        {setInfo}
                      </span>
                    )}
                    {progressValue !== undefined &&
                      totalSets &&
                      completedSets !== undefined && (
                        <div
                          className="h-1 bg-surface-secondary rounded-full flex-1"
                          data-testid="nav-progress-bar"
                        >
                          <div
                            className="h-full bg-gradient-to-r from-brand-gold-start to-brand-gold-end rounded-full transition-all duration-300"
                            style={{ width: `${progressValue}%` }}
                          />
                        </div>
                      )}
                  </div>
                )}
              </div>
            ) : (
              <h1 className="text-lg font-semibold text-text-primary truncate">
                {title}
              </h1>
            )}
          </div>

          {/* Right Section */}
          <div
            className={`${isExercisePage ? 'flex-shrink-0' : 'flex-1'} flex items-center justify-end`}
          >
            {rightElement}
          </div>
        </div>
      </header>

      {/* Spacer to prevent content from going under fixed header */}
      <div className="h-[66px]" />
    </>
  )
}
