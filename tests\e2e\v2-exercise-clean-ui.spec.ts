import { test, expect } from '@playwright/test'

// Helper function to perform login
async function login(page: any, email: string, password: string) {
  await page.getByRole('textbox', { name: 'Email' }).fill(email)
  await page.locator('#password').fill(password)

  await page.waitForFunction(
    () => {
      const button = document.querySelector(
        'button[type="submit"]'
      ) as HTMLButtonElement
      return button && !button.disabled
    },
    { timeout: 10000 }
  )

  const loginButton = page.locator('button[type="submit"]')
  await loginButton.waitFor({ state: 'visible', timeout: 10000 })
  await loginButton.click({ force: true, timeout: 10000 })
}

test.describe('V2 Exercise Page - Clean UI', () => {
  test('should display clean UI without backgrounds and borders', async ({
    page,
  }) => {
    // Go to login page
    await page.goto('/login')

    // Login
    await login(page, '<EMAIL>', 'Dr123456')

    // Wait for redirect to program page
    await page.waitForURL('/program', { timeout: 30000 })

    // Navigate to workout page
    await page.goto('/workout')

    // Start workout if needed
    const startButton = page.locator('button:has-text("Start Workout")')
    if (await startButton.isVisible()) {
      await startButton.click()
      await page.waitForTimeout(2000)
    }

    // Navigate to V2 exercise page
    await page.goto('/workout/exercise-v2/1')

    // Wait for the page to load
    await page.waitForSelector('[data-testid="current-set-card"]')

    // Check that reps input has clean styling
    const repsInput = await page.locator('input[aria-label="Reps"]')
    await expect(repsInput).toBeVisible()

    // Check that reps label is positioned on the number
    const repsLabel = await page.locator('text=reps')
    await expect(repsLabel).toBeVisible()
    await expect(repsLabel).toHaveClass(/absolute/)

    // Check that weight input has clean styling
    const weightInput = await page.locator('input[aria-label="Weight"]')
    await expect(weightInput).toBeVisible()

    // Check that only unit label is shown (no "Weight" label)
    await expect(page.locator('text=Weight')).not.toBeVisible()
    const unitLabel = await page.locator('text=lbs')
    await expect(unitLabel).toBeVisible()
    await expect(unitLabel).toHaveClass(/absolute/)

    // Check that arrow buttons don't have backgrounds
    const decrementReps = await page.locator(
      'button[aria-label="Decrease reps"]'
    )
    await expect(decrementReps).toBeVisible()
    await expect(decrementReps).toHaveClass(/rounded-full/)
    await expect(decrementReps).not.toHaveClass(/border/)

    // Check that quick buttons are not present
    const allButtons = await page.locator('button').all()
    const buttonTexts = await Promise.all(
      allButtons.map(async (btn) => await btn.textContent())
    )

    // Should not have any numeric quick buttons like "5", "8", "10", etc.
    const quickButtonNumbers = ['5', '8', '10', '12', '15']
    quickButtonNumbers.forEach((num) => {
      expect(buttonTexts).not.toContain(num)
    })

    // Test arrow functionality
    const initialReps = await repsInput.inputValue()
    await decrementReps.click()
    const newReps = await repsInput.inputValue()
    expect(Number(newReps)).toBe(Number(initialReps) - 1)

    // Verify save button is visible and works
    const saveButton = await page.locator('button:has-text("Save set")')
    await expect(saveButton).toBeVisible()
    await expect(saveButton).toHaveClass(/bg-gradient-metallic-gold/)
  })

  test('should have proper spacing and alignment', async ({ page }) => {
    // Go to login page
    await page.goto('/login')

    // Login
    await login(page, '<EMAIL>', 'Dr123456')

    // Wait for redirect to program page
    await page.waitForURL('/program', { timeout: 30000 })

    // Navigate to workout page
    await page.goto('/workout')

    // Start workout if needed
    const startButton = page.locator('button:has-text("Start Workout")')
    if (await startButton.isVisible()) {
      await startButton.click()
      await page.waitForTimeout(2000)
    }

    // Navigate to V2 exercise page
    await page.goto('/workout/exercise-v2/1')

    // Wait for the page to load
    await page.waitForSelector('[data-testid="current-set-card"]')

    // Check that the card has reduced padding
    const card = await page.locator('[data-testid="current-set-card"]')
    await expect(card).toHaveClass(/p-4/)

    // Check that input controls have proper gap
    const inputContainer = await page.locator(
      '[data-testid="input-controls-container"]'
    )
    await expect(inputContainer).toHaveClass(/gap-8/)

    // Take a screenshot for visual verification
    await page.screenshot({
      path: 'test-results/v2-exercise-clean-ui.png',
      fullPage: false,
    })
  })
})
