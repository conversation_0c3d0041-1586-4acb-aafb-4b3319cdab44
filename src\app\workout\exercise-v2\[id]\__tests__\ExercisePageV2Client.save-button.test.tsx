import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { ExercisePageV2Client } from '../ExercisePageV2Client'
import type { ExerciseModel, RecommendationModel } from '@/types'

// Mock dependencies
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: vi.fn(),
    replace: vi.fn(),
  }),
}))

vi.mock('@/contexts/NavigationContext', () => ({
  useNavigation: () => ({
    setTitle: vi.fn(),
  }),
}))

vi.mock('@/stores/authStore', () => ({
  useAuthStore: () => ({
    getCachedUserInfo: () => ({ MassUnit: 'lbs' }),
  }),
}))

const mockUseWorkout = vi.fn()
const mockUseWorkoutStore = vi.fn()
const mockUseSetScreenLogic = vi.fn()
const mockUseExerciseV2Actions = vi.fn()

vi.mock('@/hooks/useWorkout', () => ({
  useWorkout: () => mockUseWorkout(),
}))

vi.mock('@/stores/workoutStore', () => ({
  useWorkoutStore: () => mockUseWorkoutStore(),
}))

vi.mock('@/hooks/useExercisePageInitialization', () => ({
  useExercisePageInitialization: () => ({
    isInitializing: false,
    loadingError: null,
    retryInitialization: vi.fn(),
  }),
}))

vi.mock('@/hooks/useSetScreenLogic', () => ({
  useSetScreenLogic: () => mockUseSetScreenLogic(),
}))

vi.mock('@/hooks/useExerciseV2Actions', () => ({
  useExerciseV2Actions: () => mockUseExerciseV2Actions(),
}))

vi.mock('@/components/workout-v2/RestTimer', () => ({
  RestTimer: () => null,
  useRestTimer: () => ({ startRestTimer: vi.fn() }),
}))

vi.mock('@/utils/generateAllSets', () => ({
  generateAllSets: () => [
    {
      Id: 1,
      ExerciseId: 1,
      IsNext: true,
      IsWarmups: false,
      IsFinished: false,
      Reps: 10,
      Weight: { Kg: 45, Lb: 100 },
    },
    {
      Id: 2,
      ExerciseId: 1,
      IsNext: false,
      IsWarmups: false,
      IsFinished: false,
      Reps: 10,
      Weight: { Kg: 45, Lb: 100 },
    },
  ],
}))

const mockExercise: ExerciseModel = {
  Id: 1,
  Label: 'Bench Press',
  IsBodyweight: false,
  IsTimeBased: false,
  Timer: 0,
  IsFinished: false,
}

const mockRecommendation: RecommendationModel = {
  ExerciseId: 1,
  Reps: 10,
  Weight: { Kg: 45, Lb: 100 },
  Series: 3,
  WarmupsCount: 1,
}

describe('ExercisePageV2Client - Save Button', () => {
  const mockHandleCompleteSet = vi.fn()
  const mockHandleSkipSet = vi.fn()
  const mockHandleSaveSet = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()

    // Mock useWorkout
    mockUseWorkout.mockReturnValue({
      isLoadingWorkout: false,
      workoutError: null,
      workoutSession: { id: 'session-123' },
    })

    // Mock useWorkoutStore
    mockUseWorkoutStore.mockReturnValue({
      loadingStates: new Map(),
    })

    // Mock useSetScreenLogic
    mockUseSetScreenLogic.mockReturnValue({
      currentExercise: mockExercise,
      exercises: [mockExercise],
      currentSetIndex: 0,
      isSaving: false,
      saveError: null,
      showComplete: false,
      showExerciseComplete: false,
      recommendation: mockRecommendation,
      isLoading: false,
      error: null,
      isLastExercise: false,
      isLastSet: false,
      isWarmup: false,
      isFirstWorkSet: false,
      completedSets: [],
      setData: { reps: 10, weight: 100, duration: 0 },
      setSetData: vi.fn(),
      setSaveError: vi.fn(),
      handleSaveSet: mockHandleSaveSet,
      refetchRecommendation: vi.fn(),
    })

    // Mock useExerciseV2Actions
    mockUseExerciseV2Actions.mockReturnValue({
      handleCompleteSet: mockHandleCompleteSet,
      handleSkipSet: mockHandleSkipSet,
    })
  })

  it('should call handleCompleteSet when save button is clicked', async () => {
    // Given: Component is rendered with all required data
    render(<ExercisePageV2Client exerciseId={1} />)

    // When: User clicks save button
    const saveButton = await screen.findByRole('button', { name: /save set/i })
    await userEvent.click(saveButton)

    // Then: handleCompleteSet should be called
    await waitFor(() => {
      expect(mockHandleCompleteSet).toHaveBeenCalledTimes(1)
    })
  })

  it('should show loading state when currentExercise is null', () => {
    // Given: currentExercise is null
    mockUseSetScreenLogic.mockReturnValue({
      currentExercise: null, // null exercise
      exercises: [],
      currentSetIndex: 0,
      isSaving: false,
      saveError: null,
      showComplete: false,
      showExerciseComplete: false,
      recommendation: mockRecommendation,
      isLoading: false,
      error: null,
      isLastExercise: false,
      isLastSet: false,
      isWarmup: false,
      isFirstWorkSet: false,
      completedSets: [],
      setData: { reps: 10, weight: 100, duration: 0 },
      setSetData: vi.fn(),
      setSaveError: vi.fn(),
      handleSaveSet: mockHandleSaveSet,
      refetchRecommendation: vi.fn(),
    })

    // When: Component is rendered
    render(<ExercisePageV2Client exerciseId={1} />)

    // Then: Should show loading state, not the save button
    expect(
      screen.queryByRole('button', { name: /save set/i })
    ).not.toBeInTheDocument()
    expect(screen.getByText('Loading exercise data...')).toBeInTheDocument()
  })

  it('should show loading state when workoutSession is null', () => {
    // Given: workoutSession is null
    mockUseWorkout.mockReturnValue({
      isLoadingWorkout: false,
      workoutError: null,
      workoutSession: null, // null session
    })

    // When: Component is rendered
    render(<ExercisePageV2Client exerciseId={1} />)

    // Then: Should show loading state, not the save button
    expect(
      screen.queryByRole('button', { name: /save set/i })
    ).not.toBeInTheDocument()
    expect(screen.getByText('Loading exercise data...')).toBeInTheDocument()
  })

  it('should disable save button when isSaving is true', async () => {
    // Given: isSaving is true
    mockUseSetScreenLogic.mockReturnValue({
      currentExercise: mockExercise,
      exercises: [mockExercise],
      currentSetIndex: 0,
      isSaving: true, // Saving in progress
      saveError: null,
      showComplete: false,
      showExerciseComplete: false,
      recommendation: mockRecommendation,
      isLoading: false,
      error: null,
      isLastExercise: false,
      isLastSet: false,
      isWarmup: false,
      isFirstWorkSet: false,
      completedSets: [],
      setData: { reps: 10, weight: 100, duration: 0 },
      setSetData: vi.fn(),
      setSaveError: vi.fn(),
      handleSaveSet: mockHandleSaveSet,
      refetchRecommendation: vi.fn(),
    })

    // When: Component is rendered
    render(<ExercisePageV2Client exerciseId={1} />)

    // Then: Save button should be disabled
    const saveButton = await screen.findByRole('button', { name: /save set/i })
    expect(saveButton).toBeDisabled()
  })

  it('should log debug information when showing loading state', () => {
    // Given: Console log spy
    const consoleLogSpy = vi.spyOn(console, 'log').mockImplementation(() => {})

    // And: workoutSession is null
    mockUseWorkout.mockReturnValue({
      isLoadingWorkout: false,
      workoutError: null,
      workoutSession: null,
    })

    // When: Component is rendered
    render(<ExercisePageV2Client exerciseId={1} />)

    // Then: Should log debug information about missing data
    expect(consoleLogSpy).toHaveBeenCalledWith(
      '[ExercisePageV2Client] Showing loading state - missing data:',
      expect.objectContaining({
        hasWorkoutSession: false,
        hasCurrentExercise: true,
        hasRecommendation: true,
      })
    )

    consoleLogSpy.mockRestore()
  })
})
