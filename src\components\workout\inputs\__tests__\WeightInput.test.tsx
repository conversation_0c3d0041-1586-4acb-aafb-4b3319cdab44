import React from 'react'
import { render, screen } from '@testing-library/react'
import { describe, expect, it, vi } from 'vitest'
import { WeightInput } from '../WeightInput'

const defaultProps = {
  weight: 100,
  unit: 'lbs' as const,
  onChange: vi.fn(),
  onIncrement: vi.fn(),
  onDecrement: vi.fn(),
}

describe('WeightInput', () => {
  describe('Clean UI implementation', () => {
    it('should not display "Weight" label text', () => {
      render(<WeightInput {...defaultProps} />)

      // Label should not be visible
      expect(screen.queryByText('Weight')).not.toBeInTheDocument()
    })

    it('should only display uppercase unit label below the number', () => {
      render(<WeightInput {...defaultProps} />)

      // Unit should be visible
      const unitText = screen.getByText('LBS')
      expect(unitText).toBeInTheDocument()
      // Should not have any other labels
      expect(screen.queryByText('Additional Weight')).not.toBeInTheDocument()
    })

    it('should have clean arrow buttons without backgrounds', () => {
      render(<WeightInput {...defaultProps} />)

      const decrementBtn = screen.getByRole('button', {
        name: 'Decrease weight',
      })
      const incrementBtn = screen.getByRole('button', {
        name: 'Increase weight',
      })

      // Should not have background or border classes
      expect(decrementBtn).not.toHaveClass('bg-bg-secondary')
      expect(decrementBtn).not.toHaveClass('border')
      expect(incrementBtn).not.toHaveClass('bg-bg-secondary')
      expect(incrementBtn).not.toHaveClass('border')
    })

    it('should have clean input without background', () => {
      render(<WeightInput {...defaultProps} />)

      const input = screen.getByRole('spinbutton', { name: 'Weight' })

      // Should not have background class
      expect(input).not.toHaveClass('bg-bg-secondary')
      expect(input).not.toHaveClass('border')
    })

    it('should render uppercase unit label with proper styling', () => {
      render(<WeightInput {...defaultProps} />)

      const unitText = screen.getByText('LBS')
      expect(unitText).toHaveClass('absolute') // Positioned on/near the number
    })
  })

  it('renders with uppercase unit label on the number', () => {
    render(<WeightInput {...defaultProps} />)

    const unitLabel = screen.getByText('LBS')
    expect(unitLabel).toHaveClass('text-white/60')
  })

  it('renders input with clean styling', () => {
    render(<WeightInput {...defaultProps} />)

    const input = screen.getByRole('spinbutton', { name: 'Weight' })
    expect(input).toHaveClass('text-white')
    expect(input).toHaveClass('bg-transparent')
    expect(input).not.toHaveClass('border')
  })

  it('renders uppercase unit text with proper styling', () => {
    render(<WeightInput {...defaultProps} />)

    const unitText = screen.getByText('LBS')
    expect(unitText).toHaveClass('text-white/60')
  })

  it('renders arrow buttons without backgrounds', () => {
    render(<WeightInput {...defaultProps} />)

    const buttons = screen.getAllByRole('button')
    buttons.forEach((button) => {
      expect(button).not.toHaveClass('bg-bg-secondary')
      expect(button).not.toHaveClass('border')
      expect(button).toHaveClass('rounded-full')
    })
  })

  it('maintains clean focus styles', () => {
    render(<WeightInput {...defaultProps} />)

    const input = screen.getByRole('spinbutton', { name: 'Weight' })
    expect(input).toHaveClass('focus:outline-none')
  })

  it('shows error state with semantic error color', () => {
    render(<WeightInput {...defaultProps} error="Invalid weight" />)

    const errorText = screen.getByRole('alert')
    // Clean UI doesn't show error borders on input
    expect(errorText).toHaveClass('text-error')
  })

  it('shows disabled state with opacity', () => {
    render(<WeightInput {...defaultProps} disabled />)

    const input = screen.getByRole('spinbutton', { name: 'Weight' })
    const buttons = screen.getAllByRole('button')

    expect(input).toHaveClass('opacity-50')
    expect(input).toHaveClass('cursor-not-allowed')

    buttons.forEach((button) => {
      expect(button).toHaveClass('opacity-30')
      expect(button).toHaveClass('cursor-not-allowed')
    })
  })

  it('renders uppercase unit label consistently', () => {
    render(<WeightInput {...defaultProps} />)

    // Should only show uppercase unit label
    const unitText = screen.getByText('LBS')
    expect(unitText).toBeInTheDocument()
    expect(screen.queryByText('Additional Weight')).not.toBeInTheDocument()
    expect(screen.queryByText('Weight')).not.toBeInTheDocument()
  })
})
