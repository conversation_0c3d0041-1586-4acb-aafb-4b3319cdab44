'use client'

import { SetScreenLoadingState } from '@/components/workout/SetScreenLoadingState'
import { SetScreenErrorState } from '@/components/workout/SetScreenErrorState'
import {
  ExercisePageErrorBoundary,
  WorkoutErrorBoundary,
} from '@/components/workout/ExercisePageErrorBoundary'
import { ExerciseCompleteViewV2 } from './ExerciseCompleteViewV2'
import { WorkoutCompleteView } from '@/components/workout/WorkoutCompleteView'
import type {
  ExerciseModel,
  WorkoutLogSerieModel,
  RecommendationModel,
} from '@/types'

interface ExercisePageStatesProps {
  loadingError: Error | string | null
  workoutError: Error | null
  retryInitialization: () => Promise<void>
  isInitializing: boolean
  isLoadingWorkout: boolean
  isLoadingRecommendation: boolean
  isLoading: boolean
  recommendation: RecommendationModel | null
  currentExercise: ExerciseModel | null
  workoutSession: unknown
  error: string | null
  refetchRecommendation: () => Promise<void>
  showComplete: boolean
  showExerciseComplete: boolean
  currentSet: WorkoutLogSerieModel | null
  isLastExercise: boolean
  handleSaveSet: () => void
}

export function ExercisePageStates({
  loadingError,
  workoutError,
  retryInitialization,
  isInitializing,
  isLoadingWorkout,
  isLoadingRecommendation,
  isLoading,
  recommendation,
  currentExercise,
  workoutSession,
  error,
  refetchRecommendation,
  showComplete,
  showExerciseComplete,
  currentSet,
  isLastExercise,
  handleSaveSet,
}: ExercisePageStatesProps) {
  // Show error state with retry option
  if (loadingError) {
    const errorObj =
      typeof loadingError === 'string' ? new Error(loadingError) : loadingError
    return (
      <ExercisePageErrorBoundary
        error={errorObj}
        onRetry={retryInitialization}
      />
    )
  }

  // Handle workout errors
  if (workoutError) {
    return <WorkoutErrorBoundary error={workoutError} />
  }

  // Show loading while initializing or if we don't have all required data
  if (
    isInitializing ||
    isLoadingWorkout ||
    isLoadingRecommendation ||
    isLoading ||
    !recommendation ||
    !currentExercise ||
    !workoutSession
  ) {
    return <SetScreenLoadingState />
  }

  // Error state
  if (error) {
    return <SetScreenErrorState onRetry={refetchRecommendation} />
  }

  // Workout complete
  if (showComplete) {
    return <WorkoutCompleteView />
  }

  // Exercise complete - only show if we have loaded data and exercise is truly complete
  if (showExerciseComplete || (recommendation && !currentSet)) {
    return (
      <ExerciseCompleteViewV2
        exercise={currentExercise}
        isLastExercise={isLastExercise}
        onContinue={handleSaveSet}
      />
    )
  }

  return null
}
