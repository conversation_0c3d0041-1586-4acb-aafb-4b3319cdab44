name: 'Setup Playwright with Enhanced Stability'
description: 'Installs and verifies Playwright browsers with comprehensive error handling'
inputs:
  browsers:
    description: 'Browsers to install (space-separated)'
    required: false
    default: 'webkit chromium'
  skip-install:
    description: 'Skip browser installation (use existing cache)'
    required: false
    default: 'false'

runs:
  using: 'composite'
  steps:
    - name: Update CA certificates and test network connectivity
      shell: bash
      run: |
        echo "=== Network and SSL Setup ==="

        # Update CA certificates for Ubuntu
        if [[ "$RUNNER_OS" == "Linux" ]]; then
          echo "Updating CA certificates..."
          sudo apt-get update -qq
          sudo apt-get install -y ca-certificates curl
          sudo update-ca-certificates
        fi

        # Test HTTPS connectivity
        echo "Testing HTTPS connectivity..."
        if curl -I --connect-timeout 10 --max-time 30 https://www.google.com; then
          echo "✅ HTTPS connectivity working"
        else
          echo "❌ HTTPS connectivity failed"
          echo "Network diagnostics:"
          ping -c 3 ******* || echo "Ping to ******* failed"
          nslookup google.com || echo "DNS lookup failed"
        fi

        # Test Playwright CDN connectivity
        echo "Testing Playwright CDN connectivity..."
        if curl -I --connect-timeout 10 --max-time 30 https://playwright.azureedge.net; then
          echo "✅ Playwright CDN accessible"
        else
          echo "❌ Playwright CDN not accessible"
        fi

    - name: Check system resources before Playwright install
      shell: bash
      run: |
        echo "=== Pre-install System Check ==="
        echo "Disk usage:"
        df -h
        echo ""
        echo "Available memory:"
        if [[ "$RUNNER_OS" == "macOS" ]]; then
          vm_stat | head -5
        else
          free -h
        fi
        echo ""
        echo "Playwright cache permissions:"
        ls -ld ~/.cache/ms-playwright || echo "Cache directory does not exist yet"
        echo ""
        echo "Temp directory permissions:"
        ls -ld /tmp || true

    - name: Check disk space requirements
      shell: bash
      run: |
        # Check if we have at least 2GB free space
        AVAILABLE_KB=$(df / | tail -1 | awk '{print $4}')
        REQUIRED_KB=2097152  # 2GB in KB
        
        if [ "$AVAILABLE_KB" -lt "$REQUIRED_KB" ]; then
          echo "❌ Insufficient disk space: ${AVAILABLE_KB}KB available, ${REQUIRED_KB}KB required"
          echo "Available space: $(($AVAILABLE_KB / 1024))MB"
          echo "Required space: $(($REQUIRED_KB / 1024))MB"
          exit 1
        else
          echo "✅ Sufficient disk space: $(($AVAILABLE_KB / 1024))MB available"
        fi

    - name: Ensure PLAYWRIGHT_BROWSERS_PATH is unset
      shell: bash
      run: |
        echo "Ensuring PLAYWRIGHT_BROWSERS_PATH is unset..."
        unset PLAYWRIGHT_BROWSERS_PATH
        echo "PLAYWRIGHT_BROWSERS_PATH status: ${PLAYWRIGHT_BROWSERS_PATH:-'(unset)'}"

    - name: Clean and install Playwright browsers with retry
      if: inputs.skip-install != 'true'
      shell: bash
      run: |
        echo "Installing Playwright browsers: ${{ inputs.browsers }}"
        unset PLAYWRIGHT_BROWSERS_PATH

        # Determine the correct cache path based on OS
        if [[ "$RUNNER_OS" == "macOS" ]]; then
          CACHE_PATH="$HOME/Library/Caches/ms-playwright"
        else
          CACHE_PATH="$HOME/.cache/ms-playwright"
        fi

        echo "Using cache path: $CACHE_PATH"

        # Clean existing installations
        npx playwright uninstall --all || true
        rm -rf "$CACHE_PATH" || true
        rm -rf ~/.cache/ms-playwright || true  # Also clean Linux path just in case
        rm -rf /tmp/playwright-* || true
        mkdir -p "$CACHE_PATH" || true

        # Set environment variables for faster downloads
        export PLAYWRIGHT_DOWNLOAD_CONNECTION_TIMEOUT=60000
        export PLAYWRIGHT_DOWNLOAD_PROGRESS_TIMEOUT=60000

        # Install with retry logic using optimized settings
        for i in {1..3}; do
          echo "Installation attempt $i/3"

          # Use timeout to prevent hanging downloads
          if timeout 600 npx playwright install --with-deps ${{ inputs.browsers }}; then
            echo "✅ Browser installation successful on attempt $i"
            break
          else
            echo "❌ Installation attempt $i failed or timed out"
            if [ $i -lt 3 ]; then
              echo "Cleaning up before retry..."
              rm -rf "$CACHE_PATH" || true
              rm -rf ~/.cache/ms-playwright || true
              # Test network connectivity before retry
              echo "Testing network before retry..."
              curl -I --connect-timeout 5 --max-time 10 https://playwright.azureedge.net || echo "CDN not reachable"
              sleep 10
            else
              echo "All installation attempts failed"
              echo "Final network test:"
              curl -v --connect-timeout 5 --max-time 10 https://playwright.azureedge.net || true
              exit 1
            fi
          fi
        done

    - name: Verify browser binaries exist
      shell: bash
      run: |
        echo "Verifying Playwright browser binaries..."
        echo "DEBUG: Running on $RUNNER_OS"

        # Determine the correct cache path based on OS
        if [[ "$RUNNER_OS" == "macOS" ]]; then
          CACHE_PATH="$HOME/Library/Caches/ms-playwright"
        else
          CACHE_PATH="$HOME/.cache/ms-playwright"
        fi

        echo "Using cache path: $CACHE_PATH"

        if [ ! -d "$CACHE_PATH" ]; then
          echo "❌ Playwright cache folder missing at $CACHE_PATH"
          exit 1
        fi

        echo "Cache directory structure:"
        ls -la "$CACHE_PATH/" || true

        # Simple directory-based verification (no find command needed)
        echo "Checking for browser directories..."
        BROWSER_DIRS_FOUND=0

        for browser in ${{ inputs.browsers }}; do
          echo "Checking for $browser..."
          if [ "$browser" = "webkit" ]; then
            if ls "$CACHE_PATH"/webkit-* 1> /dev/null 2>&1; then
              echo "✅ WebKit directory found"
              BROWSER_DIRS_FOUND=$((BROWSER_DIRS_FOUND + 1))
            else
              echo "❌ WebKit directory not found"
            fi
          elif [ "$browser" = "chromium" ]; then
            if ls "$CACHE_PATH"/chromium-* 1> /dev/null 2>&1; then
              echo "✅ Chromium directory found"
              BROWSER_DIRS_FOUND=$((BROWSER_DIRS_FOUND + 1))
            else
              echo "❌ Chromium directory not found"
            fi
          fi
        done

        # Count expected browsers
        EXPECTED_BROWSERS=$(echo "${{ inputs.browsers }}" | wc -w)

        if [ "$BROWSER_DIRS_FOUND" -eq "$EXPECTED_BROWSERS" ]; then
          echo "✅ All $EXPECTED_BROWSERS browser directories found"
        else
          echo "❌ Only found $BROWSER_DIRS_FOUND out of $EXPECTED_BROWSERS expected browser directories"
          exit 1
        fi

    - name: Verify installation
      shell: bash
      run: |
        echo "Verifying browser installation..."
        unset PLAYWRIGHT_BROWSERS_PATH

        # Determine the correct cache path based on OS
        if [[ "$RUNNER_OS" == "macOS" ]]; then
          CACHE_PATH="$HOME/Library/Caches/ms-playwright"
        else
          CACHE_PATH="$HOME/.cache/ms-playwright"
        fi

        echo "Checking browser directories in $CACHE_PATH..."

        # Check if browser directories exist and contain files
        VERIFICATION_PASSED=true
        for browser in ${{ inputs.browsers }}; do
          if [ "$browser" = "webkit" ]; then
            if ls "$CACHE_PATH"/webkit-* 1> /dev/null 2>&1; then
              WEBKIT_DIR=$(ls -d "$CACHE_PATH"/webkit-* | head -1)
              if [ -d "$WEBKIT_DIR" ] && [ "$(ls -A "$WEBKIT_DIR" 2>/dev/null)" ]; then
                echo "✅ WebKit directory found and contains files: $WEBKIT_DIR"
              else
                echo "❌ WebKit directory is empty: $WEBKIT_DIR"
                VERIFICATION_PASSED=false
              fi
            else
              echo "❌ WebKit directory not found"
              VERIFICATION_PASSED=false
            fi
          elif [ "$browser" = "chromium" ]; then
            if ls "$CACHE_PATH"/chromium-* 1> /dev/null 2>&1; then
              CHROMIUM_DIR=$(ls -d "$CACHE_PATH"/chromium-* | head -1)
              if [ -d "$CHROMIUM_DIR" ] && [ "$(ls -A "$CHROMIUM_DIR" 2>/dev/null)" ]; then
                echo "✅ Chromium directory found and contains files: $CHROMIUM_DIR"
              else
                echo "❌ Chromium directory is empty: $CHROMIUM_DIR"
                VERIFICATION_PASSED=false
              fi
            else
              echo "❌ Chromium directory not found"
              VERIFICATION_PASSED=false
            fi
          fi
        done

        # Final verification
        if [ "$VERIFICATION_PASSED" = true ]; then
          echo "✅ All browser installations verified successfully"
          echo "Running Playwright version check..."
          npx playwright --version
        else
          echo "❌ Browser installation verification failed"
          echo "Cache directory contents:"
          ls -la "$CACHE_PATH"/ || echo "Cache directory not accessible"
          exit 1
        fi

    - name: Fallback reinstall on failure
      if: failure()
      shell: bash
      run: |
        echo "=== FALLBACK: Force clean reinstall ==="
        echo "Removing node_modules and reinstalling..."
        rm -rf node_modules/
        npm ci

        # Determine the correct cache path based on OS
        if [[ "$RUNNER_OS" == "macOS" ]]; then
          CACHE_PATH="$HOME/Library/Caches/ms-playwright"
        else
          CACHE_PATH="$HOME/.cache/ms-playwright"
        fi

        echo "Force cleaning all Playwright data..."
        rm -rf "$CACHE_PATH"
        rm -rf ~/.cache/ms-playwright  # Also clean the Linux path just in case
        rm -rf /tmp/playwright-*

        echo "Reinstalling Playwright browsers..."
        npx playwright install --with-deps --force ${{ inputs.browsers }}

        echo "Final verification..."
        npx playwright --version
        echo "Browser directories installed:"
        ls -la "$CACHE_PATH/" || echo "No cache directory found at $CACHE_PATH"

        # Simple verification without find command
        for browser in ${{ inputs.browsers }}; do
          if [ "$browser" = "webkit" ]; then
            if ls "$CACHE_PATH"/webkit-* 1> /dev/null 2>&1; then
              echo "✅ WebKit reinstalled successfully"
            else
              echo "❌ WebKit reinstall failed"
            fi
          elif [ "$browser" = "chromium" ]; then
            if ls "$CACHE_PATH"/chromium-* 1> /dev/null 2>&1; then
              echo "✅ Chromium reinstalled successfully"
            else
              echo "❌ Chromium reinstall failed"
            fi
          fi
        done
