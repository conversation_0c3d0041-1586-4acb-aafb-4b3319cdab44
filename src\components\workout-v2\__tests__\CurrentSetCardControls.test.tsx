import { describe, it, expect, vi } from 'vitest'
import { render, screen } from '@testing-library/react'
import { CurrentSetCardControls } from '../CurrentSetCardControls'

describe('CurrentSetCardControls', () => {
  const defaultProps = {
    setData: { reps: 10, weight: 100, duration: 0 },
    onSetDataChange: vi.fn(),
    unit: 'kg' as const,
    isSaving: false,
    animationClass: '',
  }

  describe('Fade-in animation', () => {
    it('should apply 200ms fade-in animation class when animationClass is provided', () => {
      render(
        <CurrentSetCardControls {...defaultProps} animationClass="fade-in" />
      )

      const container = screen.getByTestId('input-controls-container')
      expect(container).toHaveClass('fade-in')
    })

    it('should render without animation class when not provided', () => {
      render(<CurrentSetCardControls {...defaultProps} />)

      const container = screen.getByTestId('input-controls-container')
      expect(container).not.toHaveClass('fade-in')
    })
  })

  it('renders reps and weight sections', () => {
    render(<CurrentSetCardControls {...defaultProps} />)

    expect(screen.getByTestId('reps-section')).toBeInTheDocument()
    expect(screen.getByTestId('weight-section')).toBeInTheDocument()
  })
})
