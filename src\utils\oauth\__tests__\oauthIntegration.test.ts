/**
 * Unit tests for OAuth Integration Layer
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import type {
  OAuthUserData,
  OAuthError,
  OAuthSuccessCallback,
  OAuthErrorCallback,
} from '../../../types/oauth'
import { OAuthIntegration } from '../oauthIntegration'
import { GoogleOAuthHelper } from '../googleOAuth'
import { AppleOAuthHelper } from '../appleOAuth'
import { OAuthProviderHandlers } from '../providerHandlers'
import { OAuthErrorHandler } from '../errorHandler'
import { OAuthPerformanceTracker } from '../performanceTracker'
import { OAuthValidator } from '../validator'
import { PerformanceMonitor } from '../../performance'
import { logger } from '../../logger'
import { oauthApi } from '@/api/oauth'
import { useAuthStore } from '@/stores/authStore'

// Mock dependencies
vi.mock('../googleOAuth')
vi.mock('../appleOAuth')
vi.mock('../firebaseOAuth')
vi.mock('../providerHandlers')
vi.mock('../../performance')
vi.mock('../../logger')
vi.mock('@/api/oauth')
vi.mock('../errorHandler')
vi.mock('../performanceTracker')
vi.mock('../validator')
vi.mock('@/stores/authStore')

describe('OAuthIntegration', () => {
  beforeEach(() => {
    vi.clearAllMocks()

    // Mock authStore - it's a default export function
    ;(useAuthStore as any).getState = vi.fn().mockReturnValue({
      setAuth: vi.fn(),
    })

    // Mock OAuth API - these are named exports
    vi.mocked(oauthApi.googleLogin).mockResolvedValue({
      userName: '<EMAIL>',
      token: 'mock-token',
      userId: 'user-123',
    })
    vi.mocked(oauthApi.appleLogin).mockResolvedValue({
      userName: '<EMAIL>',
      token: 'mock-token',
      userId: 'user-123',
    })

    // Mock OAuthPerformanceTracker static methods
    vi.mocked(OAuthPerformanceTracker.markStart).mockReturnValue('start-mark')
    vi.mocked(OAuthPerformanceTracker.markComplete).mockReturnValue(undefined)
    vi.mocked(OAuthPerformanceTracker.markPerformance).mockReturnValue(
      undefined
    )

    // Mock OAuthValidator static methods
    vi.mocked(OAuthValidator.validateUserData).mockReturnValue(undefined)
    vi.mocked(OAuthValidator.sanitizeUserData).mockImplementation(
      (data) => data
    )
    vi.mocked(OAuthValidator.validateConfig).mockReturnValue({
      isValid: true,
      warnings: [],
    })

    // Mock OAuthErrorHandler static methods
    vi.mocked(OAuthErrorHandler.normalizeError).mockImplementation(
      (error, provider) => {
        if (typeof error === 'object' && error !== null && 'code' in error) {
          return error
        }
        const message = error?.message || String(error)
        let code = 'oauth_failed'
        if (message.includes('network') || message.includes('fetch')) {
          code = 'network_error'
          return {
            code,
            message:
              'Network connection failed. Please check your internet connection.',
            provider,
            providerError: error,
            details: {
              originalError: message,
              stack: error?.stack,
            },
          }
        }
        if (message.includes('cancel')) {
          code = 'user_cancelled'
          return {
            code,
            message: 'Sign-in was cancelled',
            provider,
            providerError: error,
            details: {
              originalError: message,
              stack: error?.stack,
            },
          }
        }
        if (message.includes('token')) {
          code = 'invalid_token'
          return {
            code,
            message: 'Invalid authentication response',
            provider,
            providerError: error,
            details: {
              originalError: message,
              stack: error?.stack,
            },
          }
        }
        if (message.includes('SDK')) {
          code = 'provider_error'
          return {
            code,
            message: `Failed to initialize ${provider} sign-in`,
            provider,
            providerError: error,
            details: {
              originalError: message,
              stack: error?.stack,
            },
          }
        }
        return {
          code,
          message: typeof error === 'string' ? error : 'Authentication failed',
          provider,
          providerError: error,
          details: {
            originalError: String(error),
          },
        }
      }
    )
    vi.mocked(OAuthErrorHandler.createErrorHandler).mockImplementation(
      (callback, provider) => {
        return (error: any) => {
          const normalizedError = OAuthErrorHandler.normalizeError(
            error,
            provider
          )
          callback(normalizedError)
        }
      }
    )
    vi.mocked(OAuthErrorHandler.createErrorHandler).mockImplementation(
      (callback, provider) => {
        return (error: any) => {
          const normalizedError = OAuthErrorHandler.normalizeError(
            error,
            provider
          )
          callback(normalizedError)
        }
      }
    )

    // Mock OAuthProviderHandlers static methods
    vi.mocked(OAuthProviderHandlers.initializeProvider).mockResolvedValue(
      undefined
    )
    vi.mocked(OAuthProviderHandlers.signInWithProvider).mockImplementation(
      () => {}
    )
    vi.mocked(OAuthProviderHandlers.isProviderAvailable).mockImplementation(
      (provider) => provider !== 'facebook'
    )
    vi.mocked(OAuthProviderHandlers.isProviderReady).mockImplementation(
      (provider) => provider !== 'facebook'
    )
    vi.mocked(OAuthProviderHandlers.getProviderDisplayName).mockImplementation(
      (provider) => {
        switch (provider) {
          case 'google':
            return 'Google'
          case 'apple':
            return 'Apple'
          default:
            return provider
        }
      }
    )

    // Mock logger methods
    vi.mocked(logger.info).mockImplementation(() => {})
    vi.mocked(logger.warn).mockImplementation(() => {})
    vi.mocked(logger.error).mockImplementation(() => {})

    // Mock PerformanceMonitor methods
    vi.mocked(PerformanceMonitor.mark).mockImplementation(() => {})
    vi.mocked(PerformanceMonitor.measure).mockImplementation(() => {})
    vi.mocked(PerformanceMonitor.getEntries).mockReturnValue([])

    // Reset static state
    OAuthIntegration.initialize({})
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('initialize', () => {
    it('should initialize with configuration', () => {
      const config = {
        google: {
          clientId: 'test-google-client-id',
          autoSelect: true,
        },
        apple: {
          clientId: 'test-apple-client-id',
          redirectUri: 'https://test.com/auth/apple/callback',
        },
      }

      OAuthIntegration.initialize(config)

      expect(logger.info).toHaveBeenCalledWith(
        'OAuth Integration initialized',
        {
          providers: ['google', 'apple'],
          hasGoogleConfig: true,
          hasAppleConfig: true,
        }
      )
    })

    it('should handle empty configuration', () => {
      OAuthIntegration.initialize({})

      expect(logger.info).toHaveBeenCalledWith(
        'OAuth Integration initialized',
        {
          providers: [],
          hasGoogleConfig: false,
          hasAppleConfig: false,
        }
      )
    })
  })

  describe('handleOAuthSuccess', () => {
    const mockUserData: OAuthUserData = {
      provider: 'google',
      providerId: 'google-123',
      email: '<EMAIL>',
      emailVerified: true,
      name: 'Test User',
      firstName: 'Test',
      lastName: 'User',
      pictureUrl: 'https://example.com/picture.jpg',
      rawToken: 'mock-jwt-token',
      tokenPayload: {
        sub: 'google-123',
        email: '<EMAIL>',
        email_verified: true,
        name: 'Test User',
        given_name: 'Test',
        family_name: 'User',
        picture: 'https://example.com/picture.jpg',
        iat: **********,
        exp: **********,
        iss: 'https://accounts.google.com',
        aud: 'test-client-id',
      },
      metadata: {
        selectBy: 'user',
      },
    }

    it('should process successful OAuth data', async () => {
      const result = await OAuthIntegration.handleOAuthSuccess(
        mockUserData,
        'google'
      )

      expect(result).toEqual({
        provider: 'google',
        token: 'mock-jwt-token',
        authorizationCode: undefined,
        state: undefined,
        deviceInfo: {
          platform: 'web',
          userAgent: navigator.userAgent,
        },
      })

      expect(PerformanceMonitor.mark).toHaveBeenCalledWith(
        'oauth-google-process-start'
      )
      expect(PerformanceMonitor.mark).toHaveBeenCalledWith(
        'oauth-google-process-complete'
      )
      expect(PerformanceMonitor.measure).toHaveBeenCalled()
    })

    it('should handle Apple-specific metadata', async () => {
      const appleUserData: OAuthUserData = {
        ...mockUserData,
        provider: 'apple',
        metadata: {
          authorizationCode: 'apple-auth-code',
          state: 'apple-state',
        },
      }

      const result = await OAuthIntegration.handleOAuthSuccess(
        appleUserData,
        'apple'
      )

      expect(result.authorizationCode).toBe('apple-auth-code')
      expect(result.state).toBe('apple-state')
    })

    it('should validate user data and throw on invalid data', async () => {
      const invalidUserData = {
        ...mockUserData,
        email: '', // Invalid empty email
      }

      await expect(
        OAuthIntegration.handleOAuthSuccess(invalidUserData, 'google')
      ).rejects.toThrow()

      expect(logger.error).toHaveBeenCalled()
    })

    it('should log success event with appropriate details', async () => {
      await OAuthIntegration.handleOAuthSuccess(mockUserData, 'google')

      expect(logger.info).toHaveBeenCalledWith('OAuth google success', {
        email: '<EMAIL>',
        emailVerified: true,
        hasName: true,
        hasProfilePicture: true,
      })
    })
  })

  describe('normalizeError', () => {
    it('should return OAuth error as-is if already normalized', () => {
      const oauthError: OAuthError = {
        code: 'user_cancelled',
        message: 'User cancelled the sign-in',
        provider: 'google',
      }

      const result = OAuthIntegration.normalizeError(oauthError, 'google')
      expect(result).toBe(oauthError)
    })

    it('should normalize network errors', () => {
      const networkError = new Error('Failed to fetch')
      const result = OAuthIntegration.normalizeError(networkError, 'google')

      expect(result).toEqual({
        code: 'network_error',
        message:
          'Network connection failed. Please check your internet connection.',
        provider: 'google',
        providerError: networkError,
        details: {
          originalError: 'Failed to fetch',
          stack: expect.any(String),
        },
      })
    })

    it('should normalize user cancellation errors', () => {
      const cancelError = new Error('User cancelled the operation')
      const result = OAuthIntegration.normalizeError(cancelError, 'apple')

      expect(result).toEqual({
        code: 'user_cancelled',
        message: 'Sign-in was cancelled',
        provider: 'apple',
        providerError: cancelError,
        details: {
          originalError: 'User cancelled the operation',
          stack: expect.any(String),
        },
      })
    })

    it('should normalize token errors', () => {
      const tokenError = new Error('Invalid token provided')
      const result = OAuthIntegration.normalizeError(tokenError, 'google')

      expect(result).toEqual({
        code: 'invalid_token',
        message: 'Invalid authentication response',
        provider: 'google',
        providerError: tokenError,
        details: {
          originalError: 'Invalid token provided',
          stack: expect.any(String),
        },
      })
    })

    it('should normalize SDK initialization errors', () => {
      const sdkError = new Error('Failed to initialize SDK')
      const result = OAuthIntegration.normalizeError(sdkError, 'apple')

      expect(result).toEqual({
        code: 'provider_error',
        message: 'Failed to initialize apple sign-in',
        provider: 'apple',
        providerError: sdkError,
        details: {
          originalError: 'Failed to initialize SDK',
          stack: expect.any(String),
        },
      })
    })

    it('should handle string errors', () => {
      const result = OAuthIntegration.normalizeError(
        'Something went wrong',
        'google'
      )

      expect(result).toEqual({
        code: 'oauth_failed',
        message: 'Something went wrong',
        provider: 'google',
        providerError: 'Something went wrong',
        details: {
          originalError: 'Something went wrong',
        },
      })
    })

    it('should handle unknown error types', () => {
      const result = OAuthIntegration.normalizeError(
        { weird: 'error' },
        'apple'
      )

      expect(result).toEqual({
        code: 'oauth_failed',
        message: 'Authentication failed',
        provider: 'apple',
        providerError: { weird: 'error' },
        details: {
          originalError: '[object Object]',
        },
      })
    })

    it('should track error performance metrics', () => {
      const error = new Error('Network failed')
      OAuthIntegration.normalizeError(error, 'google')

      expect(PerformanceMonitor.mark).toHaveBeenCalledWith(
        'oauth-google-error-oauth_failed'
      )
    })
  })

  describe('validateUserData', () => {
    it('should validate correct user data', () => {
      const validData: OAuthUserData = {
        provider: 'google',
        providerId: 'user-123',
        email: '<EMAIL>',
        emailVerified: true,
        rawToken: 'token',
        tokenPayload: {},
      }

      expect(() => OAuthIntegration.validateUserData(validData)).not.toThrow()
      expect(logger.debug).toHaveBeenCalledWith('User data validation passed', {
        provider: 'google',
        hasAllRequiredFields: true,
      })
    })

    it('should throw on missing provider', () => {
      const invalidData = {
        providerId: 'user-123',
        email: '<EMAIL>',
        rawToken: 'token',
      } as OAuthUserData

      expect(() => OAuthIntegration.validateUserData(invalidData)).toThrow(
        'Invalid user data: Provider is required'
      )
    })

    it('should throw on missing email', () => {
      const invalidData = {
        provider: 'google',
        providerId: 'user-123',
        rawToken: 'token',
      } as OAuthUserData

      expect(() => OAuthIntegration.validateUserData(invalidData)).toThrow(
        'Invalid user data: Email is required'
      )
    })

    it('should throw on invalid email format', () => {
      const invalidData: OAuthUserData = {
        provider: 'google',
        providerId: 'user-123',
        email: 'invalid-email',
        emailVerified: true,
        rawToken: 'token',
        tokenPayload: {},
      }

      expect(() => OAuthIntegration.validateUserData(invalidData)).toThrow(
        'Invalid email format'
      )
    })

    it('should throw on multiple validation errors', () => {
      const invalidData = {
        email: '<EMAIL>',
      } as OAuthUserData

      expect(() => OAuthIntegration.validateUserData(invalidData)).toThrow(
        'Invalid user data: Provider is required, Provider ID is required, Authentication token is required'
      )
    })
  })

  describe('markPerformance', () => {
    it('should mark performance for success action', () => {
      OAuthIntegration.markPerformance('google', 'success')

      expect(PerformanceMonitor.mark).toHaveBeenCalledWith(
        'oauth-google-success'
      )
      expect(logger.debug).toHaveBeenCalledWith('OAuth performance mark', {
        provider: 'google',
        action: 'success',
        errorCode: undefined,
        markName: 'oauth-google-success',
      })
    })

    it('should mark performance for error action with code', () => {
      OAuthIntegration.markPerformance('apple', 'error', 'network_error')

      expect(PerformanceMonitor.mark).toHaveBeenCalledWith(
        'oauth-apple-error-network_error'
      )
      expect(logger.debug).toHaveBeenCalledWith('OAuth performance mark', {
        provider: 'apple',
        action: 'error',
        errorCode: 'network_error',
        markName: 'oauth-apple-error-network_error',
      })
    })
  })

  describe('initializeProvider', () => {
    it('should initialize Google provider', async () => {
      const onSuccess = vi.fn()
      const onError = vi.fn()
      const config = { clientId: 'test-client-id', autoSelect: true }

      OAuthIntegration.initialize({ google: config })
      await OAuthIntegration.initializeProvider('google', onSuccess, onError)

      expect(GoogleOAuthHelper.initialize).toHaveBeenCalledWith(
        onSuccess,
        onError,
        config
      )
      expect(logger.info).toHaveBeenCalledWith(
        'OAuth provider google initialized successfully'
      )
    })

    it('should initialize Apple provider', async () => {
      const onSuccess = vi.fn()
      const onError = vi.fn()
      const config = {
        clientId: 'com.example.app',
        redirectUri: 'https://example.com/callback',
        usePopup: true,
      }

      OAuthIntegration.initialize({ apple: config })
      await OAuthIntegration.initializeProvider('apple', onSuccess, onError)

      expect(AppleOAuthHelper.initialize).toHaveBeenCalledWith(
        onSuccess,
        onError,
        config
      )
      expect(logger.info).toHaveBeenCalledWith(
        'OAuth provider apple initialized successfully'
      )
    })

    it('should handle initialization errors', async () => {
      const onSuccess = vi.fn()
      const onError = vi.fn()
      const error = new Error('Init failed')

      vi.mocked(GoogleOAuthHelper.initialize).mockRejectedValue(error)

      await expect(
        OAuthIntegration.initializeProvider('google', onSuccess, onError)
      ).rejects.toThrow()

      expect(onError).toHaveBeenCalledWith(
        expect.objectContaining({
          code: 'oauth_failed',
          message: 'Init failed',
          provider: 'google',
        })
      )
    })

    it('should throw for unsupported provider', async () => {
      const onSuccess = vi.fn()
      const onError = vi.fn()

      await expect(
        OAuthIntegration.initializeProvider(
          'facebook' as OAuthUserData['provider'],
          onSuccess,
          onError
        )
      ).rejects.toThrow('Unsupported OAuth provider: facebook')
    })
  })

  describe('signIn', () => {
    it('should sign in with Google provider', async () => {
      const onSuccess = vi.fn()
      const onError = vi.fn()

      // Mock OAuthProviderHandlers.signInWithProvider to call the success callback
      vi.mocked(OAuthProviderHandlers.signInWithProvider).mockImplementation(
        async (provider, successCb: OAuthSuccessCallback) => {
          const mockUserData: OAuthUserData = {
            provider: 'google',
            providerId: 'google-123',
            email: '<EMAIL>',
            emailVerified: true,
            rawToken: 'mock-token',
            tokenPayload: {},
          }
          await successCb(mockUserData)
        }
      )

      await OAuthIntegration.signIn('google', onSuccess, onError)

      expect(OAuthProviderHandlers.signInWithProvider).toHaveBeenCalled()
      expect(onSuccess).toHaveBeenCalledWith(
        expect.objectContaining({
          provider: 'google',
          email: '<EMAIL>',
        })
      )
      expect(logger.info).toHaveBeenCalledWith('OAuth sign-in completed', {
        provider: 'google',
        email: '<EMAIL>',
      })
    })

    it('should sign in with Apple provider', async () => {
      const onSuccess = vi.fn()
      const onError = vi.fn()

      // Mock OAuthProviderHandlers.signInWithProvider to call the success callback
      vi.mocked(OAuthProviderHandlers.signInWithProvider).mockImplementation(
        async (provider, successCb: OAuthSuccessCallback) => {
          const mockUserData: OAuthUserData = {
            provider: 'apple',
            providerId: 'apple-123',
            email: '<EMAIL>',
            emailVerified: true,
            rawToken: 'mock-token',
            tokenPayload: {},
          }
          await successCb(mockUserData)
        }
      )

      await OAuthIntegration.signIn('apple', onSuccess, onError)

      expect(OAuthProviderHandlers.signInWithProvider).toHaveBeenCalled()
      expect(onSuccess).toHaveBeenCalledWith(
        expect.objectContaining({
          provider: 'apple',
          email: '<EMAIL>',
        })
      )
    })

    it('should handle sign-in errors', async () => {
      const onSuccess = vi.fn()
      const onError = vi.fn()

      // Mock OAuthProviderHandlers.signInWithProvider to call the error callback
      vi.mocked(OAuthProviderHandlers.signInWithProvider).mockImplementation(
        async (provider, _successCb, errorCb: OAuthErrorCallback) => {
          const error: OAuthError = {
            code: 'user_cancelled',
            message: 'User cancelled',
            provider: 'google',
          }
          errorCb(error)
        }
      )

      await OAuthIntegration.signIn('google', onSuccess, onError)

      expect(onError).toHaveBeenCalledWith(
        expect.objectContaining({
          code: 'user_cancelled',
          provider: 'google',
        })
      )
    })

    it('should handle validation errors in success callback', async () => {
      const onSuccess = vi.fn()
      const onError = vi.fn()

      // Mock OAuthProviderHandlers.signInWithProvider to call success with invalid data
      vi.mocked(OAuthProviderHandlers.signInWithProvider).mockImplementation(
        async (provider, successCb: OAuthSuccessCallback) => {
          const invalidUserData = {
            provider: 'google',
            email: '', // Invalid empty email
          } as OAuthUserData
          await successCb(invalidUserData)
        }
      )

      await OAuthIntegration.signIn('google', onSuccess, onError)

      expect(onError).toHaveBeenCalledWith(
        expect.objectContaining({
          code: 'invalid_token',
          provider: 'google',
        })
      )
      expect(onSuccess).not.toHaveBeenCalled()
    })
  })

  describe('isProviderAvailable', () => {
    it('should check Google availability', () => {
      vi.mocked(OAuthProviderHandlers.isProviderAvailable).mockReturnValue(true)
      expect(OAuthIntegration.isProviderAvailable('google')).toBe(true)

      vi.mocked(OAuthProviderHandlers.isProviderAvailable).mockReturnValue(
        false
      )
      expect(OAuthIntegration.isProviderAvailable('google')).toBe(false)
    })

    it('should check Apple availability', () => {
      vi.mocked(OAuthProviderHandlers.isProviderAvailable).mockImplementation(
        (provider) => provider === 'apple'
      )
      expect(OAuthIntegration.isProviderAvailable('apple')).toBe(true)

      vi.mocked(OAuthProviderHandlers.isProviderAvailable).mockReturnValue(
        false
      )
      expect(OAuthIntegration.isProviderAvailable('apple')).toBe(false)
    })

    it('should return false for unknown provider', () => {
      expect(
        OAuthIntegration.isProviderAvailable(
          'facebook' as OAuthUserData['provider']
        )
      ).toBe(false)
    })
  })

  describe('isProviderReady', () => {
    it('should check Google readiness', () => {
      vi.mocked(OAuthProviderHandlers.isProviderReady).mockReturnValue(true)
      expect(OAuthIntegration.isProviderReady('google')).toBe(true)

      vi.mocked(OAuthProviderHandlers.isProviderReady).mockReturnValue(false)
      expect(OAuthIntegration.isProviderReady('google')).toBe(false)
    })

    it('should check Apple readiness', () => {
      vi.mocked(OAuthProviderHandlers.isProviderReady).mockImplementation(
        (provider) => provider === 'apple'
      )
      expect(OAuthIntegration.isProviderReady('apple')).toBe(true)

      vi.mocked(OAuthProviderHandlers.isProviderReady).mockReturnValue(false)
      expect(OAuthIntegration.isProviderReady('apple')).toBe(false)
    })

    it('should return false for unknown provider', () => {
      expect(
        OAuthIntegration.isProviderReady(
          'facebook' as OAuthUserData['provider']
        )
      ).toBe(false)
    })
  })

  describe('getProviderDisplayName', () => {
    it('should return correct display names', () => {
      vi.mocked(
        OAuthProviderHandlers.getProviderDisplayName
      ).mockImplementation((provider) => {
        switch (provider) {
          case 'google':
            return 'Google'
          case 'apple':
            return 'Apple'
          default:
            return provider
        }
      })
      expect(OAuthIntegration.getProviderDisplayName('google')).toBe('Google')
      expect(OAuthIntegration.getProviderDisplayName('apple')).toBe('Apple')
    })

    it('should return provider name for unknown provider', () => {
      vi.mocked(
        OAuthProviderHandlers.getProviderDisplayName
      ).mockImplementation((provider) => provider)
      expect(
        OAuthIntegration.getProviderDisplayName(
          'facebook' as OAuthUserData['provider']
        )
      ).toBe('facebook')
    })
  })

  describe('createErrorHandler', () => {
    it('should create wrapped error handler with context', () => {
      const originalOnError = vi.fn()
      const context = { userId: '123', action: 'login' }
      const wrappedHandler = OAuthIntegration.createErrorHandler(
        originalOnError,
        context
      )

      const error: OAuthError = {
        code: 'oauth_failed',
        message: 'Test error',
        provider: 'google',
      }

      wrappedHandler(error)

      expect(logger.error).toHaveBeenCalledWith('OAuth error occurred', {
        ...error,
        ...context,
        timestamp: expect.any(String),
      })
      expect(originalOnError).toHaveBeenCalledWith(error)
    })

    it('should work without context', () => {
      const originalOnError = vi.fn()
      const wrappedHandler =
        OAuthIntegration.createErrorHandler(originalOnError)

      const error: OAuthError = {
        code: 'user_cancelled',
        message: 'Cancelled',
        provider: 'apple',
      }

      wrappedHandler(error)

      expect(logger.error).toHaveBeenCalledWith('OAuth error occurred', {
        ...error,
        timestamp: expect.any(String),
      })
      expect(originalOnError).toHaveBeenCalledWith(error)
    })
  })

  describe('getPerformanceMetrics', () => {
    it('should calculate performance metrics correctly', () => {
      const mockEntries = [
        {
          name: 'oauth-google-success',
          duration: 100,
          startTime: 0,
          entryType: 'measure' as const,
          toJSON: () => ({}),
        },
        {
          name: 'oauth-google-success',
          duration: 150,
          startTime: 0,
          entryType: 'measure' as const,
          toJSON: () => ({}),
        },
        {
          name: 'oauth-apple-init',
          duration: 200,
          startTime: 0,
          entryType: 'measure' as const,
          toJSON: () => ({}),
        },
        {
          name: 'oauth-google-error-network_error',
          duration: 50,
          startTime: 0,
          entryType: 'measure' as const,
          toJSON: () => ({}),
        },
        {
          name: 'oauth-apple-error-user_cancelled',
          duration: 10,
          startTime: 0,
          entryType: 'measure' as const,
          toJSON: () => ({}),
        },
      ]

      vi.mocked(PerformanceMonitor.getEntries).mockReturnValue(mockEntries)

      const metrics = OAuthIntegration.getPerformanceMetrics()

      expect(metrics).toEqual({
        totalOperations: 5,
        byProvider: {
          google: {
            count: 3,
            totalDuration: 300,
            averageDuration: 100,
          },
          apple: {
            count: 2,
            totalDuration: 210,
            averageDuration: 105,
          },
        },
        byAction: {
          success: {
            count: 2,
            totalDuration: 250,
            averageDuration: 125,
          },
          init: {
            count: 1,
            totalDuration: 200,
            averageDuration: 200,
          },
          error: {
            count: 2,
            totalDuration: 60,
            averageDuration: 30,
          },
        },
        errors: {
          network_error: 1,
          user_cancelled: 1,
        },
      })
    })

    it('should handle empty entries', () => {
      vi.mocked(PerformanceMonitor.getEntries).mockReturnValue([])

      const metrics = OAuthIntegration.getPerformanceMetrics()

      expect(metrics).toEqual({
        totalOperations: 0,
        byProvider: {},
        byAction: {},
        errors: {},
      })
    })

    it('should filter non-OAuth entries', () => {
      const mockEntries = [
        {
          name: 'api-login',
          duration: 100,
          startTime: 0,
          entryType: 'measure' as const,
          toJSON: () => ({}),
        },
        {
          name: 'oauth-google-success',
          duration: 150,
          startTime: 0,
          entryType: 'measure' as const,
          toJSON: () => ({}),
        },
        {
          name: 'page-load',
          duration: 200,
          startTime: 0,
          entryType: 'measure' as const,
          toJSON: () => ({}),
        },
      ]

      vi.mocked(PerformanceMonitor.getEntries).mockReturnValue(mockEntries)

      const metrics = OAuthIntegration.getPerformanceMetrics()

      expect(metrics.totalOperations).toBe(1)
      expect(metrics.byProvider).toHaveProperty('google')
      expect(Object.keys(metrics.byProvider)).toHaveLength(1)
    })
  })

  describe('exported singleton', () => {
    it('should export bound methods', async () => {
      const { oauthIntegration } = await import('../oauthIntegration')

      // Test that methods are properly bound
      expect(typeof oauthIntegration.initialize).toBe('function')
      expect(typeof oauthIntegration.handleOAuthSuccess).toBe('function')
      expect(typeof oauthIntegration.normalizeError).toBe('function')
      expect(typeof oauthIntegration.validateUserData).toBe('function')
      expect(typeof oauthIntegration.markPerformance).toBe('function')
      expect(typeof oauthIntegration.initializeProvider).toBe('function')
      expect(typeof oauthIntegration.signIn).toBe('function')
      expect(typeof oauthIntegration.isProviderAvailable).toBe('function')
      expect(typeof oauthIntegration.isProviderReady).toBe('function')
      expect(typeof oauthIntegration.getProviderDisplayName).toBe('function')
      expect(typeof oauthIntegration.createErrorHandler).toBe('function')
      expect(typeof oauthIntegration.getPerformanceMetrics).toBe('function')
    })
  })
})
