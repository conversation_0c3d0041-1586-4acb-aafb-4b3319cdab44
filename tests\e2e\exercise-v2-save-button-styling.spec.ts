import { test, expect } from '@playwright/test'
import { setupAuthenticatedUser } from './helpers/auth-helper'

test.describe('Exercise V2 Save Button Styling', () => {
  test.beforeEach(async ({ page }) => {
    await setupAuthenticatedUser(page)
  })

  test('should display Save set button with correct styling matching original exercise page', async ({
    page,
  }) => {
    // Setup API mocks for exercise page
    const benchPress = {
      Id: 1,
      Label: 'Bench Press',
      IsBodyweight: false,
      IsFinished: false,
      IsNextExercise: true,
    }

    await page.route('**/GetUserProgramInfoResponseModel', async (route) => {
      await route.fulfill({
        status: 200,
        json: {
          Result: {
            NextWorkoutTemplate: {
              Id: 13602,
              Label: 'Push Day',
              ListOfExercises: [benchPress],
            },
            TodaysWorkoutId: '123',
          },
          Status: 'Success',
        },
      })
    })

    await page.route('**/GetUserCustomizedCurrentWorkout', async (route) => {
      await route.fulfill({
        status: 200,
        json: {
          Result: {
            Id: 13602,
            Label: 'Push Day',
            ListOfExercises: [benchPress],
          },
          Status: 'Success',
        },
      })
    })

    await page.route('**/GetUserWorkoutLogAverageResponse', async (route) => {
      await route.fulfill({
        status: 200,
        json: {
          Status: 'Success',
          Result: {
            Id: 1,
            ExerciseId: 1,
            Reps: 10,
            Weight: { Kg: 80, Lb: 176 },
            Series: 3,
            Increments: { Kg: 2.5, Lb: 5 },
            WarmupsCount: 2,
            WarmUpsList: [
              { WarmUpReps: 5, WarmUpWeightSet: { Kg: 40, Lb: 88 } },
              { WarmUpReps: 5, WarmUpWeightSet: { Kg: 60, Lb: 132 } },
            ],
          },
        },
      })
    })

    await page.route('**/GetWorkoutSession', async (route) => {
      await route.fulfill({
        status: 200,
        json: {
          Status: 'Success',
          Result: {
            UserId: 'test-user',
            TodaysWorkoutId: '123',
            ProgramId: 456,
            WorkoutOrder: 1,
            CreatedOn: new Date().toISOString(),
          },
        },
      })
    })

    // Navigate to exercise v2 page
    await page.goto('/workout/exercise-v2/1')

    // Wait for the page to load
    await page.waitForSelector('[data-testid="current-set-card"]')

    // Find the Save set button
    const saveButton = page.getByRole('button', { name: 'Save set' })
    await expect(saveButton).toBeVisible()

    // Verify button text
    await expect(saveButton).toHaveText('Save set')

    // Verify button has correct classes for styling
    const buttonClasses = await saveButton.getAttribute('class')

    // Check for metallic gold gradient
    expect(buttonClasses).toContain('bg-gradient-metallic-gold')

    // Check for shadow effects
    expect(buttonClasses).toContain('shadow-theme-xl')
    expect(buttonClasses).toContain('hover:shadow-theme-2xl')

    // Check for shimmer effect
    expect(buttonClasses).toContain('shimmer-hover')

    // Check for text shadow
    expect(buttonClasses).toContain('text-shadow-sm')

    // Check for sizing
    expect(buttonClasses).toContain('py-4')
    expect(buttonClasses).toContain('min-h-[56px]')

    // Check for text color
    expect(buttonClasses).toContain('text-text-inverse')

    // Check for font styling
    expect(buttonClasses).toContain('font-semibold')
    expect(buttonClasses).toContain('text-lg')

    // Verify button is enabled
    await expect(saveButton).toBeEnabled()

    // Take a screenshot for visual verification
    await page.screenshot({
      path: 'tests/e2e/screenshots/exercise-v2-save-button.png',
      clip: await saveButton.boundingBox(),
    })
  })

  test('should show disabled state with correct styling when saving', async ({
    page,
  }) => {
    // Setup same mocks as above
    const benchPress = {
      Id: 1,
      Label: 'Bench Press',
      IsBodyweight: false,
      IsFinished: false,
      IsNextExercise: true,
    }

    await page.route('**/GetUserProgramInfoResponseModel', async (route) => {
      await route.fulfill({
        status: 200,
        json: {
          Result: {
            NextWorkoutTemplate: {
              Id: 13602,
              Label: 'Push Day',
              ListOfExercises: [benchPress],
            },
            TodaysWorkoutId: '123',
          },
          Status: 'Success',
        },
      })
    })

    await page.route('**/GetUserCustomizedCurrentWorkout', async (route) => {
      await route.fulfill({
        status: 200,
        json: {
          Result: {
            Id: 13602,
            Label: 'Push Day',
            ListOfExercises: [benchPress],
          },
          Status: 'Success',
        },
      })
    })

    await page.route('**/GetUserWorkoutLogAverageResponse', async (route) => {
      await route.fulfill({
        status: 200,
        json: {
          Status: 'Success',
          Result: {
            Id: 1,
            ExerciseId: 1,
            Reps: 10,
            Weight: { Kg: 80, Lb: 176 },
            Series: 3,
            Increments: { Kg: 2.5, Lb: 5 },
            WarmupsCount: 2,
            WarmUpsList: [
              { WarmUpReps: 5, WarmUpWeightSet: { Kg: 40, Lb: 88 } },
              { WarmUpReps: 5, WarmUpWeightSet: { Kg: 60, Lb: 132 } },
            ],
          },
        },
      })
    })

    await page.route('**/GetWorkoutSession', async (route) => {
      await route.fulfill({
        status: 200,
        json: {
          Status: 'Success',
          Result: {
            UserId: 'test-user',
            TodaysWorkoutId: '123',
            ProgramId: 456,
            WorkoutOrder: 1,
            CreatedOn: new Date().toISOString(),
          },
        },
      })
    })

    // Mock the save endpoint to delay response
    await page.route('**/SaveUserWorkoutLog', async (route) => {
      // Delay to keep the button in saving state
      await new Promise((resolve) => setTimeout(resolve, 1000))
      await route.fulfill({
        status: 200,
        json: { Status: 'Success' },
      })
    })

    // Navigate to exercise v2 page
    await page.goto('/workout/exercise-v2/1')

    // Wait for the page to load
    await page.waitForSelector('[data-testid="current-set-card"]')

    // Click the Save set button
    const saveButton = page.getByRole('button', { name: 'Save set' })
    await saveButton.click()

    // Verify the button shows "Saving..." text
    await expect(saveButton).toHaveText('Saving...')

    // Verify disabled state classes
    const buttonClasses = await saveButton.getAttribute('class')
    expect(buttonClasses).toContain('opacity-60')
    expect(buttonClasses).toContain('cursor-not-allowed')
    expect(buttonClasses).toContain('bg-bg-tertiary')
    expect(buttonClasses).toContain('text-text-tertiary')

    // Verify button is disabled
    await expect(saveButton).toBeDisabled()
  })
})
