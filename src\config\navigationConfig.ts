import { RestTimerSettings } from '@/components/navigation/RestTimerSettings'

export interface NavigationConfig {
  title: string
  showBackButton: boolean
  backRoute?: string
  leftElement?: string | React.ComponentType<Record<string, unknown>>
  rightElement?: string | React.ComponentType<Record<string, unknown>>
  requiresAuth?: boolean
  confirmBeforeBack?: boolean
  backConfirmMessage?: string
}

export type RouteConfig = Record<string, NavigationConfig>

export const navigationConfig: RouteConfig = {
  '/program': {
    title: 'Home',
    showBackButton: false,
    rightElement: 'UserAvatar',
    requiresAuth: true,
  },
  '/workout': {
    title: 'Workout',
    showBackButton: true,
    backRoute: '/program',
    rightElement: 'UserAvatar',
    requiresAuth: true,
  },
  '/workout/exercise/[id]': {
    title: '', // Dynamic title from exercise name
    showBackButton: true,
    backRoute: '/workout',
    rightElement: 'UserAvatar',
    requiresAuth: true,
  },
  '/workout/exercise-v2/[id]': {
    title: '', // Dynamic title from exercise name
    showBackButton: true,
    backRoute: '/workout',
    rightElement: 'UserAvatar',
    requiresAuth: true,
  },
  '/workout/rest-timer': {
    title: 'Rest Timer',
    showBackButton: true,
    rightElement: RestTimerSettings,
    requiresAuth: true,
  },
  '/workout/complete': {
    title: 'Workout summary',
    showBackButton: true,
    backRoute: '/workout',
    rightElement: 'UserAvatar',
    requiresAuth: true,
  },
}

export function getNavigationConfig(pathname: string): NavigationConfig | null {
  // Exact match
  if (navigationConfig[pathname]) {
    return navigationConfig[pathname]
  }

  // Pattern matching for dynamic routes
  const entries = Object.entries(navigationConfig)
  const dynamicRoute = entries.find(([pattern]) => {
    if (pattern.includes('[')) {
      // Convert pattern to regex
      const regex = new RegExp(`^${pattern.replace(/\[.*?\]/g, '[^/]+')}$`)
      return regex.test(pathname)
    }
    return false
  })

  return dynamicRoute ? dynamicRoute[1] : null
}
