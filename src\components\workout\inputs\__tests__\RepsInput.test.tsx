import React from 'react'
import { render, screen } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { describe, expect, it, vi } from 'vitest'
import { RepsInput } from '../RepsInput'

const defaultProps = {
  reps: 10,
  onChange: vi.fn(),
  onIncrement: vi.fn(),
  onDecrement: vi.fn(),
}

describe('RepsInput', () => {
  describe('Clean UI implementation', () => {
    it('should not render any quick buttons', () => {
      render(<RepsInput {...defaultProps} />)

      // Should only have increment/decrement buttons, no quick buttons
      const allButtons = screen.getAllByRole('button')
      const quickButtons = allButtons.filter((btn) => {
        const label = btn.getAttribute('aria-label')
        return !label?.includes('crease')
      })

      expect(quickButtons).toHaveLength(0)
    })

    it('should not display "Reps" label text', () => {
      render(<RepsInput {...defaultProps} />)

      // Label should not be visible
      expect(screen.queryByText('Reps')).not.toBeInTheDocument()
    })

    it('should have clean arrow buttons without backgrounds', () => {
      render(<RepsInput {...defaultProps} />)

      const decrementBtn = screen.getByRole('button', { name: 'Decrease reps' })
      const incrementBtn = screen.getByRole('button', { name: 'Increase reps' })

      // Should not have background or border classes
      expect(decrementBtn).not.toHaveClass('bg-bg-secondary')
      expect(decrementBtn).not.toHaveClass('border')
      expect(incrementBtn).not.toHaveClass('bg-bg-secondary')
      expect(incrementBtn).not.toHaveClass('border')
    })

    it('should have clean input without background', () => {
      render(<RepsInput {...defaultProps} />)

      const input = screen.getByRole('spinbutton', { name: 'Reps' })

      // Should not have background class
      expect(input).not.toHaveClass('bg-bg-secondary')
      expect(input).not.toHaveClass('border')
    })

    it('should display uppercase REPS label on the number itself', () => {
      render(<RepsInput {...defaultProps} />)

      // Should have a label positioned on the number - uppercase
      const repsLabel = screen.getByText('REPS', { exact: false })
      expect(repsLabel).toBeInTheDocument()
      expect(repsLabel).toHaveClass('absolute') // Positioned on the number
    })
  })

  it('renders with uppercase REPS label on the number', () => {
    render(<RepsInput {...defaultProps} />)

    const label = screen.getByText('REPS')
    expect(label).toHaveClass('text-white/60')
  })

  it('renders input with clean styling', () => {
    render(<RepsInput {...defaultProps} />)

    const input = screen.getByRole('spinbutton', { name: 'Reps' })
    expect(input).toHaveClass('text-white')
    expect(input).toHaveClass('bg-transparent')
    expect(input).not.toHaveClass('border')
  })

  it('renders arrow buttons without backgrounds', () => {
    render(<RepsInput {...defaultProps} />)

    const buttons = screen.getAllByRole('button')
    buttons.forEach((button) => {
      expect(button).not.toHaveClass('bg-bg-secondary')
      expect(button).not.toHaveClass('border')
      expect(button).toHaveClass('rounded-full')
    })
  })

  it('maintains clean focus styles', () => {
    render(<RepsInput {...defaultProps} />)

    const input = screen.getByRole('spinbutton', { name: 'Reps' })
    expect(input).toHaveClass('focus:outline-none')
  })

  it('shows error state with semantic error color', () => {
    render(<RepsInput {...defaultProps} error="Invalid reps" />)

    const errorText = screen.getByRole('alert')
    // Clean UI doesn't show error borders on input
    expect(errorText).toHaveClass('text-error')
  })

  it('shows disabled state with opacity', () => {
    render(<RepsInput {...defaultProps} disabled />)

    const input = screen.getByRole('spinbutton', { name: 'Reps' })
    const buttons = screen.getAllByRole('button')

    expect(input).toHaveClass('opacity-50')
    expect(input).toHaveClass('cursor-not-allowed')

    buttons.forEach((button) => {
      expect(button).toHaveClass('opacity-30')
      expect(button).toHaveClass('cursor-not-allowed')
    })
  })

  describe('increment/decrement buttons', () => {
    it('renders increment and decrement buttons', () => {
      render(<RepsInput {...defaultProps} />)

      const decrementBtn = screen.getByRole('button', { name: 'Decrease reps' })
      const incrementBtn = screen.getByRole('button', { name: 'Increase reps' })

      expect(decrementBtn).toBeInTheDocument()
      expect(incrementBtn).toBeInTheDocument()
    })

    it('calls onIncrement when increment button is clicked', async () => {
      const user = userEvent.setup()
      render(<RepsInput {...defaultProps} />)

      const incrementBtn = screen.getByRole('button', { name: 'Increase reps' })
      await user.click(incrementBtn)

      expect(defaultProps.onIncrement).toHaveBeenCalledTimes(1)
    })

    it('calls onDecrement when decrement button is clicked', async () => {
      const user = userEvent.setup()
      render(<RepsInput {...defaultProps} />)

      const decrementBtn = screen.getByRole('button', { name: 'Decrease reps' })
      await user.click(decrementBtn)

      expect(defaultProps.onDecrement).toHaveBeenCalledTimes(1)
    })

    it('disables increment/decrement buttons when input is disabled', () => {
      render(<RepsInput {...defaultProps} disabled />)

      const decrementBtn = screen.getByRole('button', { name: 'Decrease reps' })
      const incrementBtn = screen.getByRole('button', { name: 'Increase reps' })

      expect(decrementBtn).toBeDisabled()
      expect(incrementBtn).toBeDisabled()
    })

    it('increment/decrement buttons have proper styling', () => {
      render(<RepsInput {...defaultProps} />)

      const decrementBtn = screen.getByRole('button', { name: 'Decrease reps' })
      const incrementBtn = screen.getByRole('button', { name: 'Increase reps' })

      // Check enabled state styling
      expect(decrementBtn).toHaveClass('opacity-60')
      expect(decrementBtn).toHaveClass('hover:opacity-100')
      expect(decrementBtn).not.toHaveClass('border')

      expect(incrementBtn).toHaveClass('opacity-60')
      expect(incrementBtn).toHaveClass('hover:opacity-100')
      expect(incrementBtn).not.toHaveClass('border')
    })

    it('increment/decrement buttons are not included in quick button count', () => {
      render(<RepsInput {...defaultProps} />)

      // Should only get quick buttons, not increment/decrement buttons
      const quickButtons = screen.getAllByRole('button').filter((btn) => {
        const label = btn.getAttribute('aria-label')
        return !label?.includes('crease')
      })

      expect(quickButtons).toHaveLength(0) // No quick buttons in clean UI
    })
  })
})
