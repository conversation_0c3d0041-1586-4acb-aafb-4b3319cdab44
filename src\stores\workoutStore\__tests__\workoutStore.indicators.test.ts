import { describe, it, expect, beforeEach } from 'vitest'
import { useWorkoutStore } from '../index'
import type { ExerciseModel, RecommendationModel } from '@/types'

describe('WorkoutStore - Exercise Status Indicators', () => {
  beforeEach(() => {
    // Reset store to initial state
    useWorkoutStore.setState({
      currentWorkout: null,
      exercises: [],
      workoutSession: null,
      exerciseRecommendations: new Map(),
      currentExerciseIndex: 0,
      currentSetIndex: 0,
    })
  })

  describe('saveSet - status updates', () => {
    it('should update exercise to IsInProgress after saving first work set', () => {
      // Given: A workout with exercises and recommendations
      const mockExercises: ExerciseModel[] = [
        {
          Id: 123,
          Label: 'Bench Press',
          IsBodyweight: false,
          IsNextExercise: true,
        } as ExerciseModel,
      ]

      const mockRecommendation: RecommendationModel = {
        Series: 3,
        Reps: 10,
        Weight: { Kg: 60, Lb: 132 },
        RIR: 2,
      } as RecommendationModel

      const store = useWorkoutStore.getState()
      store.setWorkout({
        Exercises: mockExercises,
      } as any)
      store.startWorkout()

      // Set up recommendation
      store.exerciseRecommendations.set('123', mockRecommendation)

      // When: Saving the first work set
      store.saveSet({
        ExerciseId: 123,
        Reps: 10,
        Weight: { Kg: 60, Lb: 132 },
        RIR: 2,
        IsWarmups: false,
      })

      // Then: Exercise should be marked as in progress
      const updatedState = useWorkoutStore.getState()
      const updatedExercise = updatedState.exercises[0] as any
      expect(updatedExercise.IsInProgress).toBe(true)
      expect(updatedExercise.IsFinished).toBe(false)
    })

    it('should update exercise to IsFinished after completing all recommended sets', () => {
      // Given: A workout with exercises and recommendations for 2 sets
      const mockExercises: ExerciseModel[] = [
        {
          Id: 456,
          Label: 'Squat',
          IsBodyweight: false,
        } as ExerciseModel,
      ]

      const mockRecommendation: RecommendationModel = {
        Series: 2, // Only 2 sets recommended
        Reps: 8,
        Weight: { Kg: 80, Lb: 176 },
        RIR: 1,
      } as RecommendationModel

      const store = useWorkoutStore.getState()
      store.setWorkout({
        Exercises: mockExercises,
      } as any)
      store.startWorkout()

      // Set up recommendation
      store.exerciseRecommendations.set('456', mockRecommendation)

      // When: Saving all recommended sets
      store.saveSet({
        ExerciseId: 456,
        Reps: 8,
        Weight: { Kg: 80, Lb: 176 },
        RIR: 1,
        IsWarmups: false,
      })

      store.saveSet({
        ExerciseId: 456,
        Reps: 8,
        Weight: { Kg: 80, Lb: 176 },
        RIR: 1,
        IsWarmups: false,
      })

      // Then: Exercise should be marked as finished
      const updatedState = useWorkoutStore.getState()
      const updatedExercise = updatedState.exercises[0] as any
      expect(updatedExercise.IsInProgress).toBe(false)
      expect(updatedExercise.IsFinished).toBe(true)
    })

    it('should not count warmup sets towards completion', () => {
      // Given: A workout with exercises
      const mockExercises: ExerciseModel[] = [
        {
          Id: 789,
          Label: 'Deadlift',
          IsBodyweight: false,
        } as ExerciseModel,
      ]

      const store = useWorkoutStore.getState()
      store.setWorkout({
        Exercises: mockExercises,
      } as any)
      store.startWorkout()

      // When: Saving warmup sets
      store.saveSet({
        ExerciseId: 789,
        Reps: 5,
        Weight: { Kg: 40, Lb: 88 },
        IsWarmups: true, // Warmup set
      })

      store.saveSet({
        ExerciseId: 789,
        Reps: 5,
        Weight: { Kg: 60, Lb: 132 },
        IsWarmups: true, // Another warmup
      })

      // Then: Exercise should not be marked as in progress or finished
      const updatedState = useWorkoutStore.getState()
      const updatedExercise = updatedState.exercises[0] as any
      expect(updatedExercise.IsInProgress).toBeFalsy()
      expect(updatedExercise.IsFinished).toBe(false)
    })

    it('should use default 3 sets when no recommendation available', () => {
      // Given: A workout with exercises but no recommendations
      const mockExercises: ExerciseModel[] = [
        {
          Id: 321,
          Label: 'Pull-ups',
          IsBodyweight: true,
        } as ExerciseModel,
      ]

      const store = useWorkoutStore.getState()
      store.setWorkout({
        Exercises: mockExercises,
      } as any)
      store.startWorkout()

      // No recommendation set

      // When: Saving 3 sets (default)
      for (let i = 0; i < 3; i++) {
        store.saveSet({
          ExerciseId: 321,
          Reps: 10,
          Weight: { Kg: 0, Lb: 0 },
          IsWarmups: false,
        })
      }

      // Then: Exercise should be finished after 3 sets
      const updatedState = useWorkoutStore.getState()
      const updatedExercise = updatedState.exercises[0] as any
      expect(updatedExercise.IsFinished).toBe(true)
    })

    it('should handle multiple exercises with different completion states', () => {
      // Given: Multiple exercises
      const mockExercises: ExerciseModel[] = [
        { Id: 1, Label: 'Exercise 1' } as ExerciseModel,
        { Id: 2, Label: 'Exercise 2' } as ExerciseModel,
        { Id: 3, Label: 'Exercise 3' } as ExerciseModel,
      ]

      const store = useWorkoutStore.getState()
      store.setWorkout({
        Exercises: mockExercises,
      } as any)
      store.startWorkout()

      // When: Saving different numbers of sets
      // Exercise 1: Complete (3 sets)
      for (let i = 0; i < 3; i++) {
        store.saveSet({
          ExerciseId: 1,
          Reps: 10,
          Weight: { Kg: 50, Lb: 110 },
          IsWarmups: false,
        })
      }

      // Exercise 2: In progress (1 set)
      store.saveSet({
        ExerciseId: 2,
        Reps: 10,
        Weight: { Kg: 50, Lb: 110 },
        IsWarmups: false,
      })

      // Exercise 3: Not started (0 sets)

      // Then: Each exercise should have correct status
      const updatedState = useWorkoutStore.getState()
      const [ex1, ex2, ex3] = updatedState.exercises as any[]

      expect(ex1.IsFinished).toBe(true)
      expect(ex1.IsInProgress).toBe(false)

      expect(ex2.IsFinished).toBe(false)
      expect(ex2.IsInProgress).toBe(true)

      expect(ex3.IsFinished).toBeFalsy() // undefined or false
      expect(ex3.IsInProgress).toBeFalsy() // Not started
    })
  })

  describe('onExerciseStatusUpdate callback', () => {
    it('should invoke callback with updated exercise data after saving set', () => {
      // Given: A workout with callback handler
      const mockExercises: ExerciseModel[] = [
        {
          Id: 999,
          Label: 'Dumbbell Curl',
          IsBodyweight: false,
        } as ExerciseModel,
      ]

      let callbackInvoked = false
      let updatedExerciseData: any = null

      const store = useWorkoutStore.getState()
      store.setWorkout({
        Exercises: mockExercises,
      } as any)
      store.startWorkout()

      // Set callback using setState
      useWorkoutStore.setState({
        onExerciseStatusUpdate: (exerciseId, statusData) => {
          callbackInvoked = true
          updatedExerciseData = statusData
        },
      })

      // When: Saving a set
      store.saveSet({
        ExerciseId: 999,
        Reps: 12,
        Weight: { Kg: 15, Lb: 33 },
        IsWarmups: false,
      })

      // Then: Callback should be invoked with correct data
      expect(callbackInvoked).toBe(true)
      expect(updatedExerciseData).toBeDefined()
      expect(updatedExerciseData.exerciseId).toBe(999)
      expect(updatedExerciseData.isInProgress).toBe(true)
      expect(updatedExerciseData.isFinished).toBe(false)
      expect(updatedExerciseData.completedSets).toBe(1)
      expect(updatedExerciseData.recommendedSets).toBe(3) // Default
    })
  })
})
